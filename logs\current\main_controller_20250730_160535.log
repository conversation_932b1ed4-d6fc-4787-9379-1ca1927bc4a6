2025-07-30 16:05:35,877 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:05:35,880 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:05:35,885 - modules.frequency_error_handler.FrequencyErrorHandler - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:05:35,890 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:05:35,907 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:05:35,925 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 16:05:35,932 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 16:05:35,999 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-07-30 16:05:36,023 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 16:05:36,040 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-07-30 16:05:36,063 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 16:05:36,095 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 16:05:36,132 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 16:05:36,146 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 16:05:36,150 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 16:05:36,212 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 16:05:36,224 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_160536.log
2025-07-30 16:05:36,228 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 16:05:36,231 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 16:05:36,232 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 16:05:36,232 - step_executor - INFO - ✅ 步骤执行器初始化完成
2025-07-30 16:05:36,234 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-07-30 16:05:36,244 - __main__ - INFO - ✅ 微信自动化主控制器初始化完成
2025-07-30 16:05:36,250 - __main__ - INFO - 📅 当前北京时间: 2025-07-31 00:05:36
2025-07-30 16:05:36,271 - __main__ - INFO - 🚀 微信自动化添加好友主控制程序启动
2025-07-30 16:05:36,280 - __main__ - INFO - 📅 启动时间: 2025-07-31 00:05:36
2025-07-30 16:05:36,306 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 16:05:36,309 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 16:05:36,313 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 16:05:36,314 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 16:05:36,324 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 16:05:36,328 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 16:05:36,329 - __main__ - INFO -   🔍 总发现窗口: 1
2025-07-30 16:05:36,332 - __main__ - INFO -   ✅ 有效可用窗口: 1
2025-07-30 16:05:36,337 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 16:05:36,339 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 16:05:36,340 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 16:05:36,342 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 16:05:36,344 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 2426918)
2025-07-30 16:05:36,349 - __main__ - INFO - 🎯 开始批量移动所有微信窗口到指定坐标位置...
2025-07-30 16:05:36,356 - __main__ - INFO - 📊 需要移动的窗口数量: 1
2025-07-30 16:05:36,361 - __main__ - INFO - 🔄 移动窗口 1/1: 微信 (句柄: 2426918)
2025-07-30 16:05:36,363 - __main__ - INFO - ✅ 窗口 1 移动成功
2025-07-30 16:05:36,378 - __main__ - INFO - 📊 批量窗口移动完成统计:
2025-07-30 16:05:36,384 - __main__ - INFO -   ✅ 成功移动: 1 个窗口
2025-07-30 16:05:36,395 - __main__ - INFO -   ❌ 移动失败: 0 个窗口
2025-07-30 16:05:36,398 - __main__ - INFO -   📈 成功率: 100.0%
2025-07-30 16:05:36,399 - __main__ - INFO - 🎉 批量窗口移动操作完成，所有窗口已移动到指定坐标位置
2025-07-30 16:05:36,400 - __main__ - INFO - 📂 开始加载联系人数据...
2025-07-30 16:05:38,142 - __main__ - INFO - ✅ 加载联系人数据完成
2025-07-30 16:05:38,144 - __main__ - INFO - 📊 总联系人数: 3135
2025-07-30 16:05:38,145 - __main__ - INFO - 📋 待处理联系人数: 2830
2025-07-30 16:05:38,146 - __main__ - INFO - 🔄 开始多微信窗口循环处理
2025-07-30 16:05:38,147 - __main__ - INFO - 📊 总窗口数: 1, 总联系人数: 2830
2025-07-30 16:05:38,147 - modules.frequency_error_handler.FrequencyErrorHandler - INFO - 📊 设置总窗口数量: 1
2025-07-30 16:05:38,148 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 16:05:38,149 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 16:05:38,154 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 16:05:38,155 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 16:05:38,177 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 16:05:38,179 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 16:05:38,180 - __main__ - INFO -   🔍 总发现窗口: 1
2025-07-30 16:05:38,180 - __main__ - INFO -   ✅ 有效可用窗口: 1
2025-07-30 16:05:38,193 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 16:05:38,194 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 16:05:38,195 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 16:05:38,196 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 16:05:38,197 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 2426918)
2025-07-30 16:05:38,208 - __main__ - INFO - 
============================================================
2025-07-30 16:05:38,214 - __main__ - INFO - 🎯 开始处理第 1/1 个微信窗口 (第 1 轮)
2025-07-30 16:05:38,215 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 2426918)
2025-07-30 16:05:38,216 - __main__ - INFO - 📊 全局进度：已处理 0/2830 个联系人（剩余 2830 个）
2025-07-30 16:05:38,224 - __main__ - INFO - ============================================================
2025-07-30 16:05:38,228 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 16:05:38,230 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 2426918)
2025-07-30 16:05:38,231 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 16:05:38,232 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 16:05:38,233 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 2426918)
2025-07-30 16:05:38,247 - __main__ - INFO - 🔄 检测到其他微信窗口在前台: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code
2025-07-30 16:05:38,250 - __main__ - INFO - ⏳ 等待前台窗口稳定后再激活目标窗口...
2025-07-30 16:05:39,256 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-30 16:05:40,410 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-30 16:05:40,415 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2426918
2025-07-30 16:05:40,457 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2426918) - 增强版
2025-07-30 16:05:40,792 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 16:05:40,802 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 16:05:40,821 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 16:05:40,832 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 16:05:40,845 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 16:05:40,846 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 16:05:40,848 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 16:05:40,849 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 16:05:40,867 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 16:05:40,881 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 16:05:41,092 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 16:05:41,093 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 16:05:41,094 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2426918 (API返回: None)
2025-07-30 16:05:41,396 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 16:05:41,396 - __main__ - INFO - ✅ 步骤1完成：窗口 微信 已成功激活并置顶
2025-07-30 16:05:41,397 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 16:05:42,397 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 16:05:42,398 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 16:05:42,398 - __main__ - INFO - 🔄 主界面操作尝试 1/3
2025-07-30 16:05:42,399 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 16:05:42,399 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 16:05:42,400 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 16:05:42,400 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 16:05:42,405 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 16:05:42,406 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 16:05:42,607 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 16:05:42,608 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 16:05:42,609 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 16:05:45,022 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 16:05:45,022 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 16:05:45,023 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.0 秒...
2025-07-30 16:05:47,019 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 16:05:47,221 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 16:05:47,223 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 16:05:47,223 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 16:05:49,604 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 16:05:49,605 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 16:05:49,605 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-30 16:05:52,204 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 16:05:52,406 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 16:05:52,408 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 16:05:52,409 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 16:05:54,788 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 16:05:54,788 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 16:05:54,789 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.6 秒...
2025-07-30 16:05:57,355 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 16:05:57,556 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 16:05:57,557 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 16:05:57,557 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 16:05:59,943 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 16:05:59,945 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 16:05:59,946 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.6 秒...
2025-07-30 16:06:01,500 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 16:06:01,701 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 16:06:01,701 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 16:06:01,702 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 16:06:04,088 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 16:06:04,088 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 16:06:04,089 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 16:06:04,089 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 16:06:04,090 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 16:06:04,091 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 16:06:04,091 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 16:06:04,092 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 659204, 进程: Weixin.exe)
2025-07-30 16:06:04,094 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 16:06:04,095 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 659204)
2025-07-30 16:06:04,096 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 659204) - 增强版
2025-07-30 16:06:04,401 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 16:06:04,401 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 16:06:04,402 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 16:06:04,402 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 16:06:04,402 - modules.window_manager - INFO - 📏 当前窗口位置: (796, 313), 大小: 328x454
2025-07-30 16:06:04,403 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 16:06:04,607 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 16:06:04,607 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 16:06:04,810 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 16:06:04,810 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 16:06:04,810 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 16:06:04,811 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 16:06:04,811 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 16:06:04,811 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 16:06:04,811 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 16:06:05,812 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 16:06:05,813 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 16:06:05,814 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 16:06:05,815 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 16:06:05,821 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 659204, 进程: Weixin.exe)
2025-07-30 16:06:05,825 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 16:06:05,827 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 16:06:05,828 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 16:06:05,830 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 16:06:05,830 - __main__ - INFO - ✅ 主界面操作流程执行成功
2025-07-30 16:06:05,831 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2426918
2025-07-30 16:06:05,831 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2426918) - 增强版
2025-07-30 16:06:06,138 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 16:06:06,139 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 16:06:06,139 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 16:06:06,140 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 16:06:06,140 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 16:06:06,141 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 16:06:06,141 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 16:06:06,142 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 16:06:06,142 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 16:06:06,143 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 16:06:06,345 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 16:06:06,345 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 16:06:06,347 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2426918 (API返回: None)
2025-07-30 16:06:06,648 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 16:06:06,648 - __main__ - INFO - ✅ 窗口置顶操作完成
2025-07-30 16:06:06,648 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 16:06:06,649 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 16:06:07,650 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 16:06:07,651 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 16:06:07,651 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 16:06:07,654 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_160607.log
2025-07-30 16:06:07,655 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 16:06:07,655 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 16:06:07,657 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 16:06:07,658 - __main__ - INFO - 🔄 传递全局联系人索引: 0
2025-07-30 16:06:07,658 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 16:06:07,658 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 16:06:07,660 - modules.wechat_auto_add_simple - INFO - 🎯 找到 2 个微信窗口:
2025-07-30 16:06:07,660 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 16:06:07,660 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 1 个
2025-07-30 16:06:07,661 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 16:06:07,661 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 16:06:07,662 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 16:06:07,663 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 16:06:07,663 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 659204
2025-07-30 16:06:07,664 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 659204) - 增强版
2025-07-30 16:06:07,975 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 16:06:07,975 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 16:06:07,976 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 16:06:07,976 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 16:06:07,976 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 16:06:07,977 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 16:06:07,977 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 16:06:07,977 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 16:06:08,179 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 16:06:08,180 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 16:06:08,183 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 659204 (API返回: None)
2025-07-30 16:06:08,484 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 16:06:08,484 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 16:06:08,485 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 16:06:08,485 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 16:06:08,486 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 16:06:08,487 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 16:06:08,487 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 16:06:08,492 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 16:06:08,493 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 16:06:09,067 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 16:06:09,068 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 16:06:09,407 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2830 个
2025-07-30 16:06:09,409 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2830 个 (总计: 3135 个)
2025-07-30 16:06:09,409 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 16:06:09,410 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 16:06:09,410 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:06:09,410 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:06:09,411 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:06:09,412 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:06:09,413 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:06:09,414 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 16:06:09,415 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2830
2025-07-30 16:06:09,416 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 16722368824 (闫如高)
2025-07-30 16:06:09,417 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:06:09,421 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:06:09,422 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:06:09,426 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:06:09,427 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:06:16,029 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 16722368824
2025-07-30 16:06:16,030 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 16:06:16,031 - modules.wechat_auto_add_simple - INFO - 👥 开始为 16722368824 执行添加朋友操作...
2025-07-30 16:06:16,031 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 16:06:16,032 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 16:06:16,033 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 16:06:16,035 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 16:06:16,044 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 16:06:16,056 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 16:06:16,056 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 16:06:16,057 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 16:06:16,057 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 16:06:16,058 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 16:06:16,059 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 16:06:16,061 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 16:06:16,068 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 16:06:16,071 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 16:06:16,076 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 16:06:16,082 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-30 16:06:16,101 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 16:06:16,624 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 16:06:16,628 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 16:06:16,726 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差28.84, 边缘比例0.0351
2025-07-30 16:06:16,733 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_160616.png
2025-07-30 16:06:16,738 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 16:06:16,741 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 16:06:16,744 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 16:06:16,745 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 16:06:16,746 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 16:06:16,752 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_160616.png
2025-07-30 16:06:16,756 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-30 16:06:16,758 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 16:06:16,761 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 16:06:16,762 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:06:16,763 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-30 16:06:16,764 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-30 16:06:16,766 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 16:06:16,767 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-30 16:06:16,769 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:06:16,774 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-30 16:06:16,776 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-30 16:06:16,777 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-30 16:06:16,778 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:06:16,788 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 16:06:16,790 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 16:06:16,791 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:06:16,792 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 16:06:16,793 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 16:06:16,794 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:06:16,796 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:06:16,797 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-30 16:06:16,800 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 16:06:16,811 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-30 16:06:16,841 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:06:16,846 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-30 16:06:16,853 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-30 16:06:16,857 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-30 16:06:16,868 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-30 16:06:16,871 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-30 16:06:16,877 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-30 16:06:16,878 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-30 16:06:16,881 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-30 16:06:16,887 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-30 16:06:16,891 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-30 16:06:16,895 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-30 16:06:16,896 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-30 16:06:16,898 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-30 16:06:16,907 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-30 16:06:16,909 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 16:06:16,911 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-30 16:06:16,912 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-30 16:06:16,913 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 16:06:16,914 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 16:06:16,927 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_160616.png
2025-07-30 16:06:16,928 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 16:06:16,929 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-30 16:06:16,933 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_160616.png
2025-07-30 16:06:16,996 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 16:06:17,001 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-30 16:06:17,004 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 16:06:17,005 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 16:06:17,306 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-30 16:06:18,086 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 16:06:18,087 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 16:06:18,087 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:06:18,088 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:06:18,089 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:06:18,090 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:06:18,090 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:06:18,090 - modules.wechat_auto_add_simple - INFO - ✅ 16722368824 添加朋友操作执行成功
2025-07-30 16:06:18,092 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:06:18,092 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:06:18,093 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:06:18,094 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:06:18,094 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:06:18,095 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 16:06:20,096 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 16:06:20,096 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 16:06:20,097 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 16:06:20,097 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 16:06:20,098 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 16:06:20,099 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 16:06:20,100 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 16:06:20,100 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 16:06:20,101 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 16:06:20,102 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 16722368824
2025-07-30 16:06:20,108 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 16:06:20,110 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 16:06:20,111 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 16:06:20,112 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 16:06:20,112 - modules.friend_request_window - INFO -    📱 phone: '16722368824'
2025-07-30 16:06:20,112 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 16:06:20,112 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 16:06:20,668 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 16:06:20,668 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 16:06:20,669 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 16:06:20,670 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 16:06:20,671 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 16722368824
2025-07-30 16:06:20,672 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 16:06:20,673 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 16:06:20,674 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 16:06:20,674 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 16:06:20,675 - modules.friend_request_window - INFO -    📱 手机号码: 16722368824
2025-07-30 16:06:20,676 - modules.friend_request_window - INFO -    🆔 准考证: 014425120139
2025-07-30 16:06:20,677 - modules.friend_request_window - INFO -    👤 姓名: 闫如高
2025-07-30 16:06:20,677 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 16:06:20,677 - modules.friend_request_window - INFO -    📝 备注格式: '014425120139-闫如高-2025-07-31 00:06:20'
2025-07-30 16:06:20,678 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 16:06:20,678 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014425120139-闫如高-2025-07-31 00:06:20'
2025-07-30 16:06:20,678 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 16:06:20,680 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 16:06:20,681 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-30 16:06:20,682 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:06:20,683 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:06:20,685 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:06:20,687 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:06:20,689 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:06:20,690 - modules.wechat_auto_add_simple - INFO - ✅ 16722368824 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-30 16:06:20,691 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 16722368824
2025-07-30 16:06:20,696 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:06:20,696 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:06:20,699 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:06:20,701 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:06:20,705 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:06:24,490 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2830
2025-07-30 16:06:24,490 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 16554124794 (郑宇倩)
2025-07-30 16:06:24,491 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:06:24,491 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:06:24,492 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:06:24,494 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:06:24,494 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:06:31,079 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 16554124794
2025-07-30 16:06:31,080 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 16:06:31,080 - modules.wechat_auto_add_simple - INFO - 👥 开始为 16554124794 执行添加朋友操作...
2025-07-30 16:06:31,080 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 16:06:31,081 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 16:06:31,082 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 16:06:31,083 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 16:06:31,088 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 16:06:31,090 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 16:06:31,092 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 16:06:31,092 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 16:06:31,093 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 16:06:31,093 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 16:06:31,094 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 16:06:31,095 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 16:06:31,110 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 16:06:31,113 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 16:06:31,117 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 16:06:31,127 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-30 16:06:31,130 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 16:06:31,634 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 16:06:31,635 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 16:06:31,697 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差28.76, 边缘比例0.0349
2025-07-30 16:06:31,706 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_160631.png
2025-07-30 16:06:31,713 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 16:06:31,719 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 16:06:31,722 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 16:06:31,723 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 16:06:31,724 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 16:06:31,729 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_160631.png
2025-07-30 16:06:31,731 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-30 16:06:31,738 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 16:06:31,739 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 16:06:31,740 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:06:31,741 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-30 16:06:31,742 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-30 16:06:31,744 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 16:06:31,745 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-30 16:06:31,745 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:06:31,747 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-30 16:06:31,749 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-30 16:06:31,752 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-30 16:06:31,755 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:06:31,757 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 16:06:31,758 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 16:06:31,759 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:06:31,761 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 16:06:31,762 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 16:06:31,763 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:06:31,769 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:06:31,773 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-30 16:06:31,775 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 16:06:31,776 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-30 16:06:31,779 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:06:31,790 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-30 16:06:31,793 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-30 16:06:31,804 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-30 16:06:31,807 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-30 16:06:31,811 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-30 16:06:31,814 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-30 16:06:31,821 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-30 16:06:31,823 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-30 16:06:31,825 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-30 16:06:31,827 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-30 16:06:31,828 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-30 16:06:31,829 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-30 16:06:31,832 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-30 16:06:31,833 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-30 16:06:31,838 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 16:06:31,839 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-30 16:06:31,840 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-30 16:06:31,841 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 16:06:31,843 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 16:06:31,851 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_160631.png
2025-07-30 16:06:31,854 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 16:06:31,856 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-30 16:06:31,861 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_160631.png
2025-07-30 16:06:31,888 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 16:06:31,890 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-30 16:06:31,890 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 16:06:31,891 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 16:06:32,193 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-30 16:06:32,968 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 16:06:32,969 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 16:06:32,971 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:06:32,971 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:06:32,972 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:06:32,973 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:06:32,973 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:06:32,973 - modules.wechat_auto_add_simple - INFO - ✅ 16554124794 添加朋友操作执行成功
2025-07-30 16:06:32,974 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:06:32,975 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:06:32,976 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:06:32,977 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:06:32,978 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:06:32,978 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 16:06:34,979 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 16:06:34,980 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 16:06:34,981 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 16:06:34,982 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 16:06:34,982 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 16:06:34,983 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 16:06:34,983 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 16:06:34,984 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 16:06:34,984 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 16:06:34,984 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 16554124794
2025-07-30 16:06:34,985 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 16:06:34,985 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 16:06:34,985 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 16:06:34,986 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 16:06:34,986 - modules.friend_request_window - INFO -    📱 phone: '16554124794'
2025-07-30 16:06:34,987 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 16:06:34,987 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 16:06:35,616 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 16:06:35,618 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 16:06:35,619 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 16:06:35,620 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 16:06:35,621 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 16554124794
2025-07-30 16:06:35,622 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 16:06:35,623 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 16:06:35,624 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 16:06:35,624 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 16:06:35,625 - modules.friend_request_window - INFO -    📱 手机号码: 16554124794
2025-07-30 16:06:35,625 - modules.friend_request_window - INFO -    🆔 准考证: 014225110321
2025-07-30 16:06:35,625 - modules.friend_request_window - INFO -    👤 姓名: 郑宇倩
2025-07-30 16:06:35,626 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 16:06:35,626 - modules.friend_request_window - INFO -    📝 备注格式: '014225110321-郑宇倩-2025-07-31 00:06:35'
2025-07-30 16:06:35,627 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 16:06:35,627 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014225110321-郑宇倩-2025-07-31 00:06:35'
2025-07-30 16:06:35,628 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 16:06:35,630 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 16:06:35,630 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-30 16:06:35,632 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:06:35,632 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:06:35,635 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:06:35,636 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:06:35,636 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:06:35,637 - modules.wechat_auto_add_simple - INFO - ✅ 16554124794 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-30 16:06:35,637 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 16554124794
2025-07-30 16:06:35,638 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:06:35,640 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:06:35,642 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:06:35,644 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:06:35,644 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:06:37,203 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 16:06:37,203 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 16:06:37,204 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 16:06:37,205 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 16:06:37,205 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 16:06:37,205 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 16:06:37,205 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 16:06:37,206 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 16:06:37,206 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 16:06:37,206 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-30 16:06:37,206 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 16:06:37,207 - __main__ - INFO - � 更新全局进度：已处理 2/2828 个联系人（剩余 2828 个）
2025-07-30 16:06:37,207 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 16:06:40,208 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 16:06:40,209 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 16:06:40,210 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 659204, 进程: Weixin.exe)
2025-07-30 16:06:40,211 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 16:06:40,211 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 16:06:40,213 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 16:06:40,215 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 16:06:40,218 - __main__ - INFO -   🔍 总发现窗口: 2
2025-07-30 16:06:40,223 - __main__ - INFO -   ✅ 有效可用窗口: 2
2025-07-30 16:06:40,223 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 16:06:40,224 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 16:06:40,224 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 16:06:40,224 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 16:06:40,225 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 2426918)
2025-07-30 16:06:40,225 - __main__ - INFO -   🔥 窗口 2: 添加朋友 (句柄: 659204)
2025-07-30 16:06:40,226 - __main__ - INFO - 📊 窗口切换后检查: 当前微信窗口数量=2
2025-07-30 16:06:40,226 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 16:06:40,226 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 16:06:40,228 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 659204, 进程: Weixin.exe)
2025-07-30 16:06:40,229 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 16:06:40,230 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 16:06:40,239 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 16:06:40,240 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 16:06:40,241 - __main__ - INFO -   🔍 总发现窗口: 2
2025-07-30 16:06:40,241 - __main__ - INFO -   ✅ 有效可用窗口: 2
2025-07-30 16:06:40,241 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 16:06:40,242 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 16:06:40,243 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 16:06:40,243 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 16:06:40,244 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 2426918)
2025-07-30 16:06:40,244 - __main__ - INFO -   🔥 窗口 2: 添加朋友 (句柄: 659204)
2025-07-30 16:06:40,244 - __main__ - INFO - 🔄 完成第 1 轮窗口循环，重新开始下一轮
2025-07-30 16:06:40,245 - __main__ - INFO - 📊 当前进度：已处理 2/2828 个联系人（剩余 2828 个）
2025-07-30 16:06:40,245 - __main__ - INFO - 
============================================================
2025-07-30 16:06:40,246 - __main__ - INFO - 🎯 开始处理第 1/1 个微信窗口 (第 2 轮)
2025-07-30 16:06:40,247 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 2426918)
2025-07-30 16:06:40,248 - __main__ - INFO - 📊 全局进度：已处理 2/2828 个联系人（剩余 2828 个）
2025-07-30 16:06:40,249 - __main__ - INFO - ============================================================
2025-07-30 16:06:40,251 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 16:06:40,253 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 2426918)
2025-07-30 16:06:40,253 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 16:06:40,254 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 16:06:40,254 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 2426918)
2025-07-30 16:06:40,254 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-30 16:06:41,355 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-30 16:06:41,356 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2426918
2025-07-30 16:06:41,356 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2426918) - 增强版
2025-07-30 16:06:41,660 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 16:06:41,660 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 16:06:41,661 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 16:06:41,661 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 16:06:41,661 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 16:06:41,661 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 16:06:41,662 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 16:06:41,662 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 16:06:41,662 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 16:06:41,663 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 16:06:41,864 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 16:06:41,865 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 16:06:41,866 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2426918 (API返回: None)
2025-07-30 16:06:42,166 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 16:06:42,167 - __main__ - INFO - ✅ 步骤1完成：窗口 微信 已成功激活并置顶
2025-07-30 16:06:42,167 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 16:06:43,168 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 16:06:43,168 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 16:06:43,169 - __main__ - INFO - 🔄 主界面操作尝试 1/3
2025-07-30 16:06:43,169 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 16:06:43,170 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 16:06:43,170 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 16:06:43,171 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 16:06:43,171 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 16:06:43,172 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 16:06:43,372 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 16:06:43,373 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 16:06:43,373 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 16:06:45,750 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 16:06:45,751 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 16:06:45,751 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-30 16:06:48,280 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 16:06:48,481 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 16:06:48,482 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 16:06:48,482 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
2025-07-30 16:06:50,866 - modules.main_interface - INFO - ✅ 成功点击: 通讯录按钮
2025-07-30 16:06:50,866 - modules.main_interface - INFO - ✅ [主界面操作] 步骤2: 点击通讯录按钮 执行成功
2025-07-30 16:06:50,867 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.7 秒...
2025-07-30 16:06:52,543 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤3: 点击微信主按钮 (3/5)
2025-07-30 16:06:52,744 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤3: 点击微信主按钮...
2025-07-30 16:06:52,745 - modules.main_interface - INFO - 🖱️ 点击 微信主按钮: (31, 95)
2025-07-30 16:06:52,746 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信主按钮 (31, 95)
2025-07-30 16:06:55,133 - modules.main_interface - INFO - ✅ 成功点击: 微信主按钮
2025-07-30 16:06:55,133 - modules.main_interface - INFO - ✅ [主界面操作] 步骤3: 点击微信主按钮 执行成功
2025-07-30 16:06:55,134 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.9 秒...
2025-07-30 16:06:57,017 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤4: 点击+快捷操作按钮 (4/5)
2025-07-30 16:06:57,218 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤4: 点击+快捷操作按钮...
2025-07-30 16:06:57,218 - modules.main_interface - INFO - 🖱️ 点击 +快捷操作按钮: (244, 41)
2025-07-30 16:06:57,219 - modules.main_interface - INFO - 🔴 高亮显示点击区域: +快捷操作按钮 (244, 41)
2025-07-30 16:06:59,599 - modules.main_interface - INFO - ✅ 成功点击: +快捷操作按钮
2025-07-30 16:06:59,600 - modules.main_interface - INFO - ✅ [主界面操作] 步骤4: 点击+快捷操作按钮 执行成功
2025-07-30 16:06:59,600 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 1.8 秒...
2025-07-30 16:07:01,396 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤5: 点击添加朋友选项 (5/5)
2025-07-30 16:07:01,598 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤5: 点击添加朋友选项...
2025-07-30 16:07:01,599 - modules.main_interface - INFO - 🖱️ 点击 添加朋友选项: (252, 125)
2025-07-30 16:07:01,599 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 添加朋友选项 (252, 125)
2025-07-30 16:07:03,982 - modules.main_interface - INFO - ✅ 成功点击: 添加朋友选项
2025-07-30 16:07:03,982 - modules.main_interface - INFO - 🔍 点击成功，等待添加朋友窗口出现...
2025-07-30 16:07:03,982 - modules.main_interface - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 16:07:03,983 - modules.window_manager - INFO - ⏳ 等待添加朋友窗口出现（超时: 8秒）...
2025-07-30 16:07:03,983 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 16:07:03,984 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 659204, 进程: Weixin.exe)
2025-07-30 16:07:03,985 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 16:07:03,986 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 16:07:03,988 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 16:07:03,988 - modules.window_manager - INFO - ✅ 找到添加朋友窗口: 添加朋友 (句柄: 659204)
2025-07-30 16:07:03,988 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 659204) - 增强版
2025-07-30 16:07:04,291 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 16:07:04,291 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 16:07:04,292 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 16:07:04,292 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 16:07:04,292 - modules.window_manager - INFO - 📏 当前窗口位置: (1200, 0), 大小: 328x454
2025-07-30 16:07:04,293 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 16:07:04,497 - modules.window_manager - INFO - ✅ 窗口成功移动到位置: (0, 0)
2025-07-30 16:07:04,498 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 16:07:04,699 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 16:07:04,700 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 16:07:04,700 - modules.window_manager - INFO - ✅ 添加朋友窗口激活成功
2025-07-30 16:07:04,700 - modules.main_interface - INFO - ✅ 添加朋友窗口已找到并激活
2025-07-30 16:07:04,701 - modules.main_interface - INFO - ✅ 添加朋友窗口已出现并激活
2025-07-30 16:07:04,701 - modules.main_interface - INFO - ✅ [主界面操作] 步骤5: 点击添加朋友选项 执行成功
2025-07-30 16:07:04,702 - modules.main_interface - INFO - 🔍 [主界面操作] 执行添加朋友窗口验证...
2025-07-30 16:07:05,703 - modules.main_interface - INFO - 🔍 验证添加朋友窗口可见性...
2025-07-30 16:07:05,703 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 16:07:05,705 - modules.window_manager - INFO - ✅ 找到微信窗口: 添加朋友 (句柄: 659204, 进程: Weixin.exe)
2025-07-30 16:07:05,706 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 16:07:05,706 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 16:07:05,709 - modules.window_manager - INFO - 🎯 总共找到 2 个微信窗口
2025-07-30 16:07:05,709 - modules.main_interface - INFO - ✅ 添加朋友窗口可见且在前台
2025-07-30 16:07:05,710 - modules.main_interface - INFO - ✅ [主界面操作] 添加朋友窗口验证成功
2025-07-30 16:07:05,711 - modules.main_interface - INFO - ✅ [主界面操作] 微信主界面操作流程执行完成
2025-07-30 16:07:05,714 - __main__ - INFO - ✅ 主界面操作流程执行成功
2025-07-30 16:07:05,715 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2426918
2025-07-30 16:07:05,717 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2426918) - 增强版
2025-07-30 16:07:06,027 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 16:07:06,027 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 16:07:06,028 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 16:07:06,028 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 16:07:06,029 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 16:07:06,030 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 16:07:06,030 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 16:07:06,031 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 16:07:06,031 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 16:07:06,031 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 16:07:06,232 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 16:07:06,233 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 16:07:06,235 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2426918 (API返回: None)
2025-07-30 16:07:06,535 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 16:07:06,536 - __main__ - INFO - ✅ 窗口置顶操作完成
2025-07-30 16:07:06,536 - __main__ - INFO - ✅ 步骤2：主界面操作完成
2025-07-30 16:07:06,536 - __main__ - INFO - ✅ 步骤 2 执行成功
2025-07-30 16:07:07,537 - __main__ - INFO - 📍 当前执行步骤: SIMPLE_ADD (步骤 3)
2025-07-30 16:07:07,537 - __main__ - INFO - 📞 步骤3：简单添加好友 - 窗口 1
2025-07-30 16:07:07,538 - __main__ - INFO - � 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...
2025-07-30 16:07:07,540 - modules.wechat_auto_add_simple - INFO - 📝 日志文件: logs\wechat_auto_simple_20250730_160707.log
2025-07-30 16:07:07,541 - modules.wechat_auto_add_simple - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 16:07:07,541 - modules.wechat_auto_add_simple - INFO - ✅ 微信自动添加好友脚本初始化完成
2025-07-30 16:07:07,542 - modules.wechat_auto_add_simple - INFO - 📁 Excel文件: 添加好友名单.xlsx
2025-07-30 16:07:07,542 - __main__ - INFO - 🔄 传递全局联系人索引: 2
2025-07-30 16:07:07,543 - __main__ - INFO - 🔧 执行包含窗口移动的完整自动化流程...
2025-07-30 16:07:07,543 - modules.wechat_auto_add_simple - INFO - 🚀 开始执行微信自动添加好友流程...
2025-07-30 16:07:07,547 - modules.wechat_auto_add_simple - INFO - 🎯 找到 2 个微信窗口:
2025-07-30 16:07:07,547 - modules.wechat_auto_add_simple - INFO -    📊 添加朋友窗口: 1 个
2025-07-30 16:07:07,547 - modules.wechat_auto_add_simple - INFO -    📊 主窗口: 1 个
2025-07-30 16:07:07,549 - modules.wechat_auto_add_simple - INFO -   1. 微信 (726x650) - main
2025-07-30 16:07:07,550 - modules.wechat_auto_add_simple - INFO -   2. 添加朋友 (328x454) - add_friend
2025-07-30 16:07:07,550 - modules.wechat_auto_add_simple - INFO - 🎯 使用添加朋友窗口: 添加朋友
2025-07-30 16:07:07,551 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 16:07:07,551 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 659204
2025-07-30 16:07:07,552 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 659204) - 增强版
2025-07-30 16:07:07,860 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 16:07:07,861 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 16:07:07,861 - modules.window_manager - INFO - 🔄 检测到微信窗口但未识别为主窗口: '添加朋友' (类名: Qt51514QWindowIcon)
2025-07-30 16:07:07,862 - modules.window_manager - INFO - 📍 执行位置移动操作以确保一致性...
2025-07-30 16:07:07,863 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 328x454
2025-07-30 16:07:07,863 - modules.window_manager - INFO - 🎯 目标位置: (0, 0)
2025-07-30 16:07:07,863 - modules.window_manager - INFO - ✅ 窗口已经在目标位置 (0, 0)，无需移动
2025-07-30 16:07:07,864 - modules.window_manager - INFO - ✅ 微信窗口位置移动成功
2025-07-30 16:07:08,066 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 16:07:08,066 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 16:07:08,069 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 659204 (API返回: None)
2025-07-30 16:07:08,370 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 16:07:08,370 - modules.wechat_auto_add_simple - INFO - ✅ 使用窗口管理器激活并置顶成功
2025-07-30 16:07:08,370 - modules.wechat_auto_add_simple - INFO - 👥 检测到添加朋友窗口，执行专门的处理流程...
2025-07-30 16:07:08,371 - modules.wechat_auto_add_simple - INFO - 🎯 开始添加朋友窗口专门处理...
2025-07-30 16:07:08,372 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-07-30 16:07:08,373 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持ensure_add_friend_window_visible方法
2025-07-30 16:07:08,374 - modules.wechat_auto_add_simple - WARNING - ⚠️ 窗口管理器不支持_start_add_friend_window_monitoring方法
2025-07-30 16:07:08,378 - modules.wechat_auto_add_simple - INFO - ✅ 窗口已移动到位置 (1200, 0)
2025-07-30 16:07:08,380 - modules.wechat_auto_add_simple - INFO - 📂 开始加载Excel文件: 添加好友名单.xlsx
2025-07-30 16:07:08,946 - modules.wechat_auto_add_simple - INFO - 📊 Excel文件读取成功，共 3135 行数据
2025-07-30 16:07:08,947 - modules.wechat_auto_add_simple - INFO - 📋 列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 16:07:09,233 - modules.wechat_auto_add_simple - INFO - ✅ 加载完成，待处理联系人: 2828 个
2025-07-30 16:07:09,235 - modules.wechat_auto_add_simple - INFO - 🔄 全局进度跟踪：从第 3 个联系人开始处理
2025-07-30 16:07:09,236 - modules.wechat_auto_add_simple - INFO - 📊 当前窗口待处理联系人: 2826 个
2025-07-30 16:07:09,237 - modules.wechat_auto_add_simple - INFO - 📊 待处理联系人: 2826 个 (总计: 3135 个)
2025-07-30 16:07:09,238 - modules.wechat_auto_add_simple - INFO - 📋 配置：每个窗口处理 2 个联系人后切换
2025-07-30 16:07:09,239 - modules.wechat_auto_add_simple - INFO - 🔧 调试：multi_window配置 = {'enabled': True, 'contacts_per_window': 2, 'switch_delay': 3, 'contact_delay': 1.5, 'max_windows': 5, 'window_validation': True, 'fallback_to_single': True}
2025-07-30 16:07:09,244 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:07:09,245 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:07:09,246 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:07:09,251 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:07:09,252 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:07:09,252 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化成功
2025-07-30 16:07:09,253 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 1/2826
2025-07-30 16:07:09,254 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17690134952 (穆斯塔帕·阿布都艾尼)
2025-07-30 16:07:09,255 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:07:09,258 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:07:09,261 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:07:09,264 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:07:09,266 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:07:15,842 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17690134952
2025-07-30 16:07:15,842 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 16:07:15,842 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17690134952 执行添加朋友操作...
2025-07-30 16:07:15,843 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 16:07:15,843 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 16:07:15,844 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 16:07:15,845 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 16:07:15,851 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 16:07:15,853 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 16:07:15,853 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 16:07:15,854 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 16:07:15,855 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 16:07:15,855 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 16:07:15,856 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 16:07:15,856 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 16:07:15,866 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 16:07:15,868 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 16:07:15,870 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 16:07:15,872 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-30 16:07:15,874 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 16:07:16,376 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 16:07:16,377 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 16:07:16,453 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差28.74, 边缘比例0.0350
2025-07-30 16:07:16,459 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_160716.png
2025-07-30 16:07:16,462 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 16:07:16,467 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 16:07:16,470 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 16:07:16,471 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 16:07:16,472 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 16:07:16,479 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_160716.png
2025-07-30 16:07:16,481 - WeChatAutoAdd - INFO - 底部区域原始检测到 38 个轮廓
2025-07-30 16:07:16,483 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(44,255), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 16:07:16,484 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(174,254), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 16:07:16,487 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(219,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:07:16,491 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(169,253), 尺寸4x4, 长宽比1.00, 面积16
2025-07-30 16:07:16,492 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(117,253), 尺寸3x3, 长宽比1.00, 面积9
2025-07-30 16:07:16,494 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(222,252), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 16:07:16,496 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,252), 尺寸2x4, 长宽比0.50, 面积8
2025-07-30 16:07:16,499 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:07:16,500 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(164,251), 尺寸4x5, 长宽比0.80, 面积20
2025-07-30 16:07:16,502 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,251), 尺寸9x7, 长宽比1.29, 面积63
2025-07-30 16:07:16,504 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(249,250), 尺寸11x6, 长宽比1.83, 面积66
2025-07-30 16:07:16,506 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(172,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:07:16,507 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(138,250), 尺寸2x2, 长宽比1.00, 面积4
2025-07-30 16:07:16,508 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(86,250), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 16:07:16,510 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,250), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:07:16,511 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(255,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 16:07:16,516 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(250,249), 尺寸3x2, 长宽比1.50, 面积6
2025-07-30 16:07:16,518 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(235,249), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:07:16,520 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(276,248), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:07:16,523 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(228,248), 尺寸21x9, 长宽比2.33, 面积189
2025-07-30 16:07:16,524 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(189,248), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 16:07:16,525 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,248), 尺寸6x5, 长宽比1.20, 面积30
2025-07-30 16:07:16,527 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(214,246), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:07:16,528 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,246), 尺寸13x9, 长宽比1.44, 面积117
2025-07-30 16:07:16,536 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(69,246), 尺寸1x4, 长宽比0.25, 面积4
2025-07-30 16:07:16,541 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(165,245), 尺寸2x5, 长宽比0.40, 面积10
2025-07-30 16:07:16,543 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(130,245), 尺寸34x12, 长宽比2.83, 面积408
2025-07-30 16:07:16,546 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(128,245), 尺寸5x12, 长宽比0.42, 面积60
2025-07-30 16:07:16,550 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,245), 尺寸22x12, 长宽比1.83, 面积264
2025-07-30 16:07:16,553 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(248,244), 尺寸41x13, 长宽比3.15, 面积533
2025-07-30 16:07:16,560 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(237,244), 尺寸10x6, 长宽比1.67, 面积60
2025-07-30 16:07:16,561 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(211,244), 尺寸24x12, 长宽比2.00, 面积288
2025-07-30 16:07:16,568 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(212,244), 尺寸6x3, 长宽比2.00, 面积18
2025-07-30 16:07:16,572 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(167,244), 尺寸44x13, 长宽比3.38, 面积572
2025-07-30 16:07:16,575 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(82,244), 尺寸34x13, 长宽比2.62, 面积442
2025-07-30 16:07:16,584 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(68,244), 尺寸10x2, 长宽比5.00, 面积20
2025-07-30 16:07:16,586 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(32,244), 尺寸36x13, 长宽比2.77, 面积468
2025-07-30 16:07:16,588 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 16:07:16,590 - WeChatAutoAdd - INFO - 底部区域找到 9 个按钮候选
2025-07-30 16:07:16,593 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=244
2025-07-30 16:07:16,594 - WeChatAutoAdd - INFO - 在底部找到按钮: (189, 250), 尺寸: 44x13, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-07-30 16:07:16,601 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-07-30 16:07:16,609 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_160716.png
2025-07-30 16:07:16,611 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-07-30 16:07:16,618 - WeChatAutoAdd - INFO - 开始验证按钮区域(189, 250)是否包含'添加到通讯录'文字
2025-07-30 16:07:16,622 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250730_160716.png
2025-07-30 16:07:16,643 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-07-30 16:07:16,649 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0486, 平均亮度=243.1, 亮度标准差=16.6
2025-07-30 16:07:16,651 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-07-30 16:07:16,653 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 16:07:16,954 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(189, 250) -> 屏幕坐标(1389, 250)
2025-07-30 16:07:17,765 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 16:07:17,767 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 16:07:17,768 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:07:17,768 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:07:17,770 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:07:17,771 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:07:17,771 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:07:17,772 - modules.wechat_auto_add_simple - INFO - ✅ 17690134952 添加朋友操作执行成功
2025-07-30 16:07:17,774 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:07:17,775 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:07:17,777 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:07:17,782 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:07:17,783 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:07:17,783 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 16:07:19,784 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 16:07:19,785 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 16:07:19,785 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 16:07:19,786 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 16:07:19,786 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 16:07:19,786 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 16:07:19,786 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 16:07:19,787 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 16:07:19,787 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 16:07:19,787 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17690134952
2025-07-30 16:07:19,788 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 16:07:19,788 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 16:07:19,788 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 16:07:19,788 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 16:07:19,789 - modules.friend_request_window - INFO -    📱 phone: '17690134952'
2025-07-30 16:07:19,789 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 16:07:19,789 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 16:07:20,330 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 16:07:20,331 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 16:07:20,331 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 16:07:20,332 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 16:07:20,333 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17690134952
2025-07-30 16:07:20,333 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 16:07:20,334 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 16:07:20,334 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 16:07:20,334 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 16:07:20,335 - modules.friend_request_window - INFO -    📱 手机号码: 17690134952
2025-07-30 16:07:20,335 - modules.friend_request_window - INFO -    🆔 准考证: 014425111694
2025-07-30 16:07:20,335 - modules.friend_request_window - INFO -    👤 姓名: 穆斯塔帕·阿布都艾尼
2025-07-30 16:07:20,335 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 16:07:20,336 - modules.friend_request_window - INFO -    📝 备注格式: '014425111694-穆斯塔帕·阿布都艾尼-2025-07-31 00:07:20'
2025-07-30 16:07:20,336 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 16:07:20,337 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014425111694-穆斯塔帕·阿布都艾尼-2025-07-31 00:07:20'
2025-07-30 16:07:20,337 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 16:07:20,339 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 16:07:20,340 - modules.friend_request_window - WARNING - ⚠️ 未找到好友申请窗口
2025-07-30 16:07:20,341 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:07:20,342 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:07:20,343 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:07:20,345 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:07:20,345 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:07:20,345 - modules.wechat_auto_add_simple - INFO - ✅ 17690134952 申请添加朋友窗口处理完成: 申请添加朋友窗口处理失败: 未找到好友申请窗口
2025-07-30 16:07:20,346 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 17690134952
2025-07-30 16:07:20,350 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:07:20,350 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:07:20,351 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:07:20,352 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:07:20,353 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:07:24,024 - modules.wechat_auto_add_simple - INFO - 📋 处理进度: 2/2826
2025-07-30 16:07:24,024 - modules.wechat_auto_add_simple - INFO - 📞 开始处理: 17621470175 (占凤全)
2025-07-30 16:07:24,025 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:07:24,025 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:07:24,027 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:07:24,028 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:07:24,029 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:07:30,628 - modules.wechat_auto_add_simple - INFO - 🔍 搜索结果分析完成: 17621470175
2025-07-30 16:07:30,628 - modules.wechat_auto_add_simple - INFO - 🔗 搜索完成，开始自动执行添加朋友操作...
2025-07-30 16:07:30,629 - modules.wechat_auto_add_simple - INFO - 👥 开始为 17621470175 执行添加朋友操作...
2025-07-30 16:07:30,629 - modules.wechat_auto_add_simple - INFO - 📦 成功导入添加朋友模块
2025-07-30 16:07:30,629 - modules.wechat_auto_add_simple - INFO - 🔧 正在初始化添加朋友模块...
2025-07-30 16:07:30,630 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-07-30 16:07:30,631 - modules.wechat_auto_add_simple - INFO - 🧹 清理截图目录...
2025-07-30 16:07:30,635 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-07-30 16:07:30,638 - modules.wechat_auto_add_simple - INFO - 🚀 启动添加朋友操作...
2025-07-30 16:07:30,638 - modules.wechat_auto_add_simple - INFO - 📋 功能说明:
2025-07-30 16:07:30,639 - modules.wechat_auto_add_simple - INFO -   - 自动检测微信'添加朋友'窗口
2025-07-30 16:07:30,639 - modules.wechat_auto_add_simple - INFO -   - 图像识别'添加到通讯录'文字
2025-07-30 16:07:30,640 - modules.wechat_auto_add_simple - INFO -   - 点击'添加到通讯录'按钮
2025-07-30 16:07:30,640 - modules.wechat_auto_add_simple - INFO -   - 保存截图和日志到本地
2025-07-30 16:07:30,642 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-07-30 16:07:30,650 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-07-30 16:07:30,652 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-07-30 16:07:30,654 - WeChatAutoAdd - DEBUG - 找到微信窗口: main_controller.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-07-30 16:07:30,656 - WeChatAutoAdd - INFO - 共找到 3 个微信窗口
2025-07-30 16:07:30,658 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-07-30 16:07:31,161 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-07-30 16:07:31,162 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-07-30 16:07:31,229 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差49.82, 边缘比例0.0489
2025-07-30 16:07:31,241 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250730_160731.png
2025-07-30 16:07:31,244 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-07-30 16:07:31,248 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-07-30 16:07:31,250 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-07-30 16:07:31,251 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-07-30 16:07:31,253 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-07-30 16:07:31,259 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250730_160731.png
2025-07-30 16:07:31,265 - WeChatAutoAdd - INFO - 底部区域原始检测到 26 个轮廓
2025-07-30 16:07:31,267 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,323), 尺寸128x30, 长宽比4.27, 已知特征:True
2025-07-30 16:07:31,272 - WeChatAutoAdd - INFO - 发现特殊位置按钮候选: Y=323 (距底部154像素区域)
2025-07-30 16:07:31,274 - WeChatAutoAdd - DEBUG - 特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: 100
2025-07-30 16:07:31,276 - WeChatAutoAdd - INFO - 发现已知按钮特征候选: 位置(100,323), 尺寸128x30
2025-07-30 16:07:31,287 - WeChatAutoAdd - INFO - 发现已知按钮特征: 位置(100,323), 尺寸128x30
2025-07-30 16:07:31,289 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(81,259), 尺寸8x4, 长宽比2.00, 面积32
2025-07-30 16:07:31,290 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,258), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 16:07:31,293 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(49,257), 尺寸1x5, 长宽比0.20, 面积5
2025-07-30 16:07:31,300 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(47,257), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:07:31,301 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(71,256), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 16:07:31,302 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,255), 尺寸2x3, 长宽比0.67, 面积6
2025-07-30 16:07:31,304 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(73,254), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:07:31,306 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(67,253), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:07:31,308 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(64,253), 尺寸2x1, 长宽比2.00, 面积2
2025-07-30 16:07:31,314 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(55,251), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:07:31,320 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(231,249), 尺寸35x34, 长宽比1.03, 面积1190
2025-07-30 16:07:31,323 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=68.6 (阈值:60)
2025-07-30 16:07:31,325 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(194,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 16:07:31,336 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=67.7 (阈值:60)
2025-07-30 16:07:31,338 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(157,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 16:07:31,340 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(120,249), 尺寸35x35, 长宽比1.00, 面积1225
2025-07-30 16:07:31,342 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=86.3 (阈值:60)
2025-07-30 16:07:31,344 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(52,249), 尺寸39x14, 长宽比2.79, 面积546
2025-07-30 16:07:31,350 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(51,249), 尺寸25x12, 长宽比2.08, 面积300
2025-07-30 16:07:31,352 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(48,249), 尺寸13x7, 长宽比1.86, 面积91
2025-07-30 16:07:31,353 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(151,242), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:07:31,355 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(131,238), 尺寸1x2, 长宽比0.50, 面积2
2025-07-30 16:07:31,357 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(158,236), 尺寸5x7, 长宽比0.71, 面积35
2025-07-30 16:07:31,358 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(142,233), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:07:31,359 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(144,230), 尺寸47x14, 长宽比3.36, 面积658
2025-07-30 16:07:31,364 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=81.7 (阈值:60)
2025-07-30 16:07:31,368 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(121,230), 尺寸28x13, 长宽比2.15, 面积364
2025-07-30 16:07:31,370 - WeChatAutoAdd - DEBUG - 颜色变化过大，跳过: std=80.7 (阈值:60)
2025-07-30 16:07:31,373 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(125,230), 尺寸1x1, 长宽比1.00, 面积1
2025-07-30 16:07:31,375 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-07-30 16:07:31,377 - WeChatAutoAdd - INFO - 底部区域找到 4 个按钮候选
2025-07-30 16:07:31,384 - WeChatAutoAdd - INFO - 选择已知按钮特征: Y=323, 很可能是'添加到通讯录'按钮
2025-07-30 16:07:31,385 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 338), 尺寸: 128x30, 位置得分: 5.0, 目标候选: False, 绿色按钮: False, 特殊位置: True
2025-07-30 16:07:31,386 - WeChatAutoAdd - INFO - 检测到高置信度按钮，跳过文字验证
2025-07-30 16:07:31,396 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250730_160731.png
2025-07-30 16:07:31,404 - WeChatAutoAdd - INFO - 检测到高置信度按钮（已知特征/绿色按钮），跳过文字验证
2025-07-30 16:07:31,406 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-07-30 16:07:31,715 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 338) -> 屏幕坐标(1364, 338)
2025-07-30 16:07:32,497 - WeChatAutoAdd - INFO - 按钮点击完成
2025-07-30 16:07:32,498 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-07-30 16:07:32,500 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:07:32,500 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:07:32,501 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:07:32,502 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:07:32,503 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:07:32,505 - modules.wechat_auto_add_simple - INFO - ✅ 17621470175 添加朋友操作执行成功
2025-07-30 16:07:32,506 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:07:32,506 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:07:32,508 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:07:32,509 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:07:32,509 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:07:32,512 - modules.wechat_auto_add_simple - INFO - 🔍 检查是否出现申请添加朋友窗口...
2025-07-30 16:07:34,514 - modules.wechat_auto_add_simple - INFO - 🎯 找到申请添加朋友窗口: 添加朋友
2025-07-30 16:07:34,514 - modules.wechat_auto_add_simple - INFO - 📦 成功导入申请添加朋友处理模块
2025-07-30 16:07:34,515 - modules.friend_request_window - INFO - ✅ 使用默认配置
2025-07-30 16:07:34,515 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-07-30 16:07:34,515 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-07-30 16:07:34,516 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-07-30 16:07:34,516 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-07-30 16:07:34,517 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-07-30 16:07:34,517 - modules.wechat_auto_add_simple - INFO - 🚀 调用申请添加朋友处理模块...
2025-07-30 16:07:34,518 - modules.friend_request_window - INFO - 🚀 开始执行好友申请流程: 17621470175
2025-07-30 16:07:34,518 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow调用栈:
2025-07-30 16:07:34,519 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 16:07:34,519 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 16:07:34,520 - modules.friend_request_window - INFO - 🔍 execute_friend_request_flow接收到的参数:
2025-07-30 16:07:34,520 - modules.friend_request_window - INFO -    📱 phone: '17621470175'
2025-07-30 16:07:34,521 - modules.friend_request_window - INFO -    📝 verification_msg: '' (应该被忽略)
2025-07-30 16:07:34,522 - modules.friend_request_window - INFO - 📋 从Excel中读取联系人信息...
2025-07-30 16:07:35,100 - modules.friend_request_window - INFO - ✅ Excel文件加载成功，共 3135 行数据
2025-07-30 16:07:35,100 - modules.friend_request_window - INFO - 🔧 已应用准考证号前导零修复逻辑
2025-07-30 16:07:35,100 - modules.friend_request_window - INFO - ✅ Excel字段验证通过，包含所有必要字段
2025-07-30 16:07:35,101 - modules.friend_request_window - INFO - 📊 Excel列名: ['手机号码', '姓名', '身份证', '准考证', '验证信息', '处理状态', '处理结果', '处理时间', '微信窗口', '重试次数']
2025-07-30 16:07:35,102 - modules.friend_request_window - INFO - 🔍 使用数字匹配查找手机号: 17621470175
2025-07-30 16:07:35,102 - modules.friend_request_window - INFO - 📊 查找结果: 找到 1 条匹配记录
2025-07-30 16:07:35,103 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 16:07:35,103 - modules.friend_request_window - INFO - ✅ 联系人数据验证通过
2025-07-30 16:07:35,103 - modules.friend_request_window - INFO - 📋 联系人信息提取完成:
2025-07-30 16:07:35,103 - modules.friend_request_window - INFO -    📱 手机号码: 17621470175
2025-07-30 16:07:35,104 - modules.friend_request_window - INFO -    🆔 准考证: 014425110034
2025-07-30 16:07:35,104 - modules.friend_request_window - INFO -    👤 姓名: 占凤全
2025-07-30 16:07:35,104 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 16:07:35,104 - modules.friend_request_window - INFO -    📝 备注格式: '014425110034-占凤全-2025-07-31 00:07:35'
2025-07-30 16:07:35,105 - modules.friend_request_window - INFO - 📝 使用Excel中的验证信息: '学习指导【视频作业】'
2025-07-30 16:07:35,105 - modules.friend_request_window - INFO - 📝 使用Excel中的备注信息: '014425110034-占凤全-2025-07-31 00:07:35'
2025-07-30 16:07:35,105 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 16:07:35,107 - modules.friend_request_window - INFO - ✅ 找到精确匹配的申请添加朋友窗口: '申请添加朋友' (句柄: 790136, 类名: Qt51514QWindowIcon, 大小: 360x660)
2025-07-30 16:07:35,109 - modules.friend_request_window - INFO - ✅ 找到最佳匹配窗口: '申请添加朋友' (句柄: 790136)
2025-07-30 16:07:35,109 - modules.friend_request_window - INFO -    匹配原因: 精确标题匹配 + 微信Qt窗口类名
2025-07-30 16:07:35,111 - modules.friend_request_window - INFO -    窗口大小: 360x660
2025-07-30 16:07:35,114 - modules.friend_request_window - INFO - 🔄 激活窗口: 790136
2025-07-30 16:07:35,818 - modules.friend_request_window - INFO - ✅ 窗口激活成功
2025-07-30 16:07:35,818 - modules.friend_request_window - INFO - 🔄 开始填写验证信息和备注
2025-07-30 16:07:35,819 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information调用栈:
2025-07-30 16:07:35,819 - modules.friend_request_window - INFO -    1. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:529 in _analyze_search_result
2025-07-30 16:07:35,819 - modules.friend_request_window - INFO -       add_friend_result = self._execute_auto_add_friend(phone)
2025-07-30 16:07:35,820 - modules.friend_request_window - INFO -    2. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:622 in _execute_auto_add_friend
2025-07-30 16:07:35,820 - modules.friend_request_window - INFO -       friend_request_result = self._handle_friend_request_window(phone)
2025-07-30 16:07:35,820 - modules.friend_request_window - INFO -    3. d:\程序-测试\微信7.28 - 副本 (2)\modules\wechat_auto_add_simple.py:725 in _handle_friend_request_window
2025-07-30 16:07:35,821 - modules.friend_request_window - INFO -       result = processor.execute_friend_request_flow(
2025-07-30 16:07:35,821 - modules.friend_request_window - INFO -    4. d:\程序-测试\微信7.28 - 副本 (2)\modules\friend_request_window.py:1176 in execute_friend_request_flow
2025-07-30 16:07:35,821 - modules.friend_request_window - INFO -       if self._fill_and_submit_information(excel_verification, excel_remark):
2025-07-30 16:07:35,821 - modules.friend_request_window - INFO - 🔍 _fill_and_submit_information接收到的参数:
2025-07-30 16:07:35,822 - modules.friend_request_window - INFO -    📝 verification参数: '学习指导【视频作业】' (类型: <class 'str'>, 长度: 10)
2025-07-30 16:07:35,822 - modules.friend_request_window - INFO -    📝 remark参数: '014425110034-占凤全-2025-07-31 00:07:35' (类型: <class 'str'>, 长度: 36)
2025-07-30 16:07:35,822 - modules.friend_request_window - INFO - 📝 即将填写验证信息: '学习指导【视频作业】'
2025-07-30 16:07:35,822 - modules.friend_request_window - INFO - 📝 即将填写备注信息: '014425110034-占凤全-2025-07-31 00:07:35'
2025-07-30 16:07:35,823 - modules.friend_request_window - INFO - 📝 步骤1: 填写验证信息
2025-07-30 16:07:35,825 - modules.friend_request_window - INFO - 🎯 准备填写 验证信息输入框
2025-07-30 16:07:35,825 - modules.friend_request_window - INFO -    📍 坐标: (960, 330)
2025-07-30 16:07:35,826 - modules.friend_request_window - INFO -    📝 内容: '学习指导【视频作业】'
2025-07-30 16:07:35,827 - modules.friend_request_window - INFO -    📏 内容长度: 10 字符
2025-07-30 16:07:35,829 - modules.friend_request_window - INFO -    🔤 内容编码: b'\xe5\xad\xa6\xe4\xb9\xa0\xe6\x8c\x87\xe5\xaf\xbc\xe3\x80\x90\xe8\xa7\x86\xe9\xa2\x91\xe4\xbd\x9c\xe4\xb8\x9a\xe3\x80\x91'
2025-07-30 16:07:35,832 - modules.friend_request_window - INFO - 🖱️ 点击 验证信息输入框
2025-07-30 16:07:36,780 - modules.friend_request_window - INFO - 🧹 清除 验证信息输入框 现有内容
2025-07-30 16:07:42,066 - modules.friend_request_window - INFO - ✅ 验证信息输入框 内容清除完成
2025-07-30 16:07:42,066 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到验证信息输入框
2025-07-30 16:07:42,067 - modules.friend_request_window - INFO -    📝 原始文本: '学习指导【视频作业】'
2025-07-30 16:07:42,067 - modules.friend_request_window - INFO -    📏 文本长度: 10 字符
2025-07-30 16:07:42,069 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '(1364, 252)...' (前50字符)
2025-07-30 16:07:42,383 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 16:07:42,384 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 16:07:43,286 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 16:07:43,296 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 16:07:43,298 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '学习指导【视频作业】'
2025-07-30 16:07:43,299 - modules.friend_request_window - INFO - ✅ 验证信息输入框 填写完成
2025-07-30 16:07:43,302 - modules.friend_request_window - INFO -    📝 已填写内容: '学习指导【视频作业】'
2025-07-30 16:07:43,305 - modules.friend_request_window - INFO -    � 内容长度: 10 字符
2025-07-30 16:07:43,807 - modules.friend_request_window - INFO - ✅ 验证信息填写成功: '学习指导【视频作业】'
2025-07-30 16:07:43,808 - modules.friend_request_window - INFO - 📝 步骤2: 填写备注信息
2025-07-30 16:07:43,808 - modules.friend_request_window - INFO - 🎯 准备填写 备注信息输入框
2025-07-30 16:07:43,809 - modules.friend_request_window - INFO -    📍 坐标: (960, 450)
2025-07-30 16:07:43,809 - modules.friend_request_window - INFO -    📝 内容: '014425110034-占凤全-2025-07-31 00:07:35'
2025-07-30 16:07:43,810 - modules.friend_request_window - INFO -    📏 内容长度: 36 字符
2025-07-30 16:07:43,811 - modules.friend_request_window - INFO -    🔤 内容编码: b'014425110034-\xe5\x8d\xa0\xe5\x87\xa4\xe5\x85\xa8-2025-07-31 00:07:35'
2025-07-30 16:07:43,811 - modules.friend_request_window - INFO - 🖱️ 点击 备注信息输入框
2025-07-30 16:07:44,746 - modules.friend_request_window - INFO - 🧹 清除 备注信息输入框 现有内容
2025-07-30 16:07:49,989 - modules.friend_request_window - INFO - ✅ 备注信息输入框 内容清除完成
2025-07-30 16:07:49,990 - modules.friend_request_window - INFO - 📋 使用剪贴板方法输入文本到备注信息输入框
2025-07-30 16:07:49,990 - modules.friend_request_window - INFO -    📝 原始文本: '014425110034-占凤全-2025-07-31 00:07:35'
2025-07-30 16:07:49,991 - modules.friend_request_window - INFO -    📏 文本长度: 36 字符
2025-07-30 16:07:49,992 - modules.friend_request_window - INFO -    💾 备份剪贴板内容: '(1364, 252)...' (前50字符)
2025-07-30 16:07:50,300 - modules.friend_request_window - INFO - ✅ 第1次复制成功
2025-07-30 16:07:50,301 - modules.friend_request_window - INFO - 📋 第1次粘贴尝试
2025-07-30 16:07:51,203 - modules.friend_request_window - INFO - ✅ 第1次粘贴完成
2025-07-30 16:07:51,214 - modules.friend_request_window - INFO - ✅ 剪贴板内容已恢复
2025-07-30 16:07:51,214 - modules.friend_request_window - INFO - ✅ 剪贴板输入完成: '014425110034-占凤全-2025-07-31 00:07:35'
2025-07-30 16:07:51,215 - modules.friend_request_window - INFO - ✅ 备注信息输入框 填写完成
2025-07-30 16:07:51,215 - modules.friend_request_window - INFO -    📝 已填写内容: '014425110034-占凤全-2025-07-31 00:07:35'
2025-07-30 16:07:51,215 - modules.friend_request_window - INFO -    � 内容长度: 36 字符
2025-07-30 16:07:51,716 - modules.friend_request_window - INFO - ✅ 备注信息填写成功: '014425110034-占凤全-2025-07-31 00:07:35'
2025-07-30 16:07:51,717 - modules.friend_request_window - INFO - 📝 步骤3: 点击确定按钮提交申请
2025-07-30 16:07:51,717 - modules.friend_request_window - INFO - 🎯 使用固定坐标配置:
2025-07-30 16:07:51,718 - modules.friend_request_window - INFO -    验证信息输入框: (960, 330)
2025-07-30 16:07:51,718 - modules.friend_request_window - INFO -    备注信息输入框: (960, 450)
2025-07-30 16:07:51,718 - modules.friend_request_window - INFO -    确定按钮: (910, 840)
2025-07-30 16:07:51,719 - modules.friend_request_window - INFO - ⏱️ 等待0.8秒确保信息填写完成
2025-07-30 16:07:52,519 - modules.friend_request_window - INFO - 🖱️ 准备点击确定按钮
2025-07-30 16:07:52,520 - modules.friend_request_window - INFO -    📍 坐标: (910, 840)
2025-07-30 16:07:52,520 - modules.friend_request_window - INFO - 🖱️ 点击确定按钮 (尝试 1/3)
2025-07-30 16:07:53,171 - modules.friend_request_window - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:07:53,172 - modules.friend_request_window - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:07:53,173 - modules.friend_request_window - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:07:53,174 - modules.friend_request_window - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:07:53,174 - modules.friend_request_window - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:07:53,175 - modules.friend_request_window - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-07-30 16:07:53,175 - modules.friend_request_window - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-07-30 16:07:53,175 - modules.friend_request_window - INFO - ⏱️ 检测超时时间: 5.0秒
2025-07-30 16:07:53,677 - modules.friend_request_window - INFO - 🎯 发现疑似微信错误提示窗口: Weixin (330x194)
2025-07-30 16:07:53,678 - modules.friend_request_window - INFO - 🔍 分析微信错误窗口: Weixin
2025-07-30 16:07:53,678 - modules.friend_request_window - INFO - ✅ 窗口大小匹配 (330x194): +0.4
2025-07-30 16:07:53,679 - modules.friend_request_window - INFO - ✅ 窗口标题匹配 (Weixin): +0.3
2025-07-30 16:07:53,679 - modules.friend_request_window - INFO - ✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2
2025-07-30 16:07:53,679 - modules.friend_request_window - INFO - 📊 窗口特征分析完成，总置信度: 0.90
2025-07-30 16:07:53,679 - modules.friend_request_window - INFO - 🚨 基于窗口特征检测到错误，置信度: 0.90
2025-07-30 16:07:53,680 - modules.friend_request_window - INFO - ⚠️ 检测到错误对话框
2025-07-30 16:07:53,680 - modules.friend_request_window - WARNING - ⚠️ 检测到频率错误: too_frequent
2025-07-30 16:07:53,680 - modules.friend_request_window - WARNING - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 16:07:53,680 - modules.friend_request_window - INFO - 🚀 启动完整自动化频率错误处理流程...
2025-07-30 16:07:53,680 - modules.friend_request_window - INFO - 🚀 正在调用demo_auto_handling.py模块...
2025-07-30 16:07:53,681 - modules.friend_request_window - ERROR - ❌ 未找到demo_auto_handling.py模块
2025-07-30 16:07:53,681 - modules.friend_request_window - WARNING - ⚠️ demo_auto_handling.py 处理失败，回退到原有处理方式...
2025-07-30 16:07:53,681 - modules.friend_request_window - INFO - 🚨 开始处理'操作过于频繁'错误...
2025-07-30 16:07:53,682 - modules.friend_request_window - INFO - 📋 错误类型: too_frequent
2025-07-30 16:07:53,682 - modules.friend_request_window - INFO - 📝 错误信息: 基于窗口特征检测到'操作过于频繁'错误
2025-07-30 16:07:53,683 - modules.friend_request_window - WARNING - 🚫 过滤异常小窗口: Weixin (330x194) - 可能是残留窗口
2025-07-30 16:07:53,685 - modules.friend_request_window - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:07:53,686 - modules.friend_request_window - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:07:53,688 - modules.friend_request_window - INFO - 🖥️ 当前微信窗口数量: 1
2025-07-30 16:07:53,690 - modules.friend_request_window - ERROR - 🚨 检测到单窗口环境且出现频率错误
2025-07-30 16:07:53,690 - modules.friend_request_window - ERROR - 🚨 单窗口环境下无法进行窗口切换，程序必须终止
2025-07-30 16:07:53,691 - modules.friend_request_window - ERROR - 💡 建议：启动多个微信窗口以支持自动切换功能
2025-07-30 16:07:53,691 - modules.friend_request_window - ERROR - 🚨 已设置程序终止标志，主程序将停止执行
2025-07-30 16:07:53,692 - modules.friend_request_window - ERROR - ❌ 所有频率错误处理方式都失败
2025-07-30 16:07:54,693 - modules.friend_request_window - INFO - 🔍 检测微信好友申请窗口
2025-07-30 16:07:54,696 - modules.friend_request_window - WARNING - ⚠️ 未找到'申请添加朋友'窗口
2025-07-30 16:07:54,697 - modules.friend_request_window - INFO - ✅ 确定按钮点击成功，好友申请窗口已关闭
2025-07-30 16:07:54,697 - modules.friend_request_window - INFO - ✅ 所有信息填写完成，已点击确定按钮提交申请
2025-07-30 16:07:54,697 - modules.friend_request_window - INFO - 📋 最终提交内容确认:
2025-07-30 16:07:54,697 - modules.friend_request_window - INFO -    💬 验证信息: '学习指导【视频作业】'
2025-07-30 16:07:54,698 - modules.friend_request_window - INFO -    📝 备注信息: '014425110034-占凤全-2025-07-31 00:07:35'
2025-07-30 16:07:55,198 - modules.friend_request_window - INFO - ✅ 好友申请流程执行成功
2025-07-30 16:07:55,199 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:07:55,200 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:07:55,201 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: Weixin (330x194) - 可能是残留窗口
2025-07-30 16:07:55,202 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:07:55,203 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:07:55,203 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:07:55,204 - modules.wechat_auto_add_simple - INFO - ✅ 17621470175 申请添加朋友窗口处理完成: 申请添加朋友窗口处理成功
2025-07-30 16:07:55,205 - modules.wechat_auto_add_simple - INFO - ✅ 完整流程执行成功: 17621470175
2025-07-30 16:07:55,206 - modules.wechat_auto_add_simple - INFO - ✅ 频率错误处理器初始化完成
2025-07-30 16:07:55,207 - modules.wechat_auto_add_simple - INFO - 📋 当前黑名单中有 0 个窗口
2025-07-30 16:07:55,209 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: Weixin (330x194) - 可能是残留窗口
2025-07-30 16:07:55,212 - modules.wechat_auto_add_simple - WARNING - 🚫 过滤异常小窗口: 微信 (240x82) - 可能是残留窗口
2025-07-30 16:07:55,214 - modules.wechat_auto_add_simple - INFO - 📊 有效微信窗口统计: 1 个
2025-07-30 16:07:55,215 - modules.wechat_auto_add_simple - INFO - 🖥️ 检测到系统中共有 1 个微信窗口
2025-07-30 16:07:56,716 - modules.wechat_auto_add_simple - INFO - 🔄 已处理 2 个联系人，达到每个窗口处理数量限制 (2)
2025-07-30 16:07:56,717 - modules.wechat_auto_add_simple - INFO - 🚪 结束当前窗口的联系人处理，返回主控制器切换到下一个窗口
2025-07-30 16:07:56,717 - modules.wechat_auto_add_simple - INFO - 🔄 返回主控制器，准备切换到下一个窗口
2025-07-30 16:07:56,718 - __main__ - INFO - 🔄 检测到联系人数量限制，需要切换到下一个窗口
2025-07-30 16:07:56,718 - __main__ - INFO - 📊 步骤3部分完成 - 处理: 2, 成功: 2, 失败: 0
2025-07-30 16:07:56,719 - __main__ - INFO - 🔄 当前窗口联系人处理完成，准备切换到下一个窗口
2025-07-30 16:07:56,719 - __main__ - INFO - 🔄 步骤 3 达到联系人处理数量限制，需要切换窗口
2025-07-30 16:07:56,719 - __main__ - INFO - 📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤
2025-07-30 16:07:56,719 - __main__ - INFO - 🔄 准备切换到下一个微信窗口并重新开始完整流程
2025-07-30 16:07:56,719 - __main__ - INFO - 🔄 第 1 个微信窗口达到联系人处理数量限制
2025-07-30 16:07:56,720 - __main__ - INFO - 📋 当前窗口联系人处理完成，准备切换到下一个微信窗口
2025-07-30 16:07:56,721 - __main__ - INFO - � 更新全局进度：已处理 4/2826 个联系人（剩余 2826 个）
2025-07-30 16:07:56,721 - __main__ - INFO - ⏳ 正常窗口切换延迟（3秒）...
2025-07-30 16:07:59,721 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 16:07:59,722 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 16:07:59,724 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 16:07:59,725 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 16:07:59,728 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 16:07:59,729 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 16:07:59,731 - __main__ - INFO -   🔍 总发现窗口: 1
2025-07-30 16:07:59,732 - __main__ - INFO -   ✅ 有效可用窗口: 1
2025-07-30 16:07:59,733 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 16:07:59,738 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 16:07:59,739 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 16:07:59,741 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 16:07:59,742 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 2426918)
2025-07-30 16:07:59,747 - __main__ - INFO - 📊 窗口切换后检查: 当前微信窗口数量=1
2025-07-30 16:07:59,750 - __main__ - INFO - 🔍 开始扫描微信窗口...
2025-07-30 16:07:59,753 - modules.window_manager - INFO - 🔍 开始搜索所有微信窗口...
2025-07-30 16:07:59,755 - modules.window_manager - INFO - ✅ 识别为新版微信Qt主窗口: 726x650, 面积=471900
2025-07-30 16:07:59,758 - modules.window_manager - INFO - ✅ 找到微信窗口: 微信 (句柄: 2426918, 进程: Weixin.exe)
2025-07-30 16:07:59,766 - modules.window_manager - INFO - 🎯 总共找到 1 个微信窗口
2025-07-30 16:07:59,767 - __main__ - INFO - 📊 窗口扫描统计:
2025-07-30 16:07:59,768 - __main__ - INFO -   🔍 总发现窗口: 1
2025-07-30 16:07:59,768 - __main__ - INFO -   ✅ 有效可用窗口: 1
2025-07-30 16:07:59,768 - __main__ - INFO -   🚫 黑名单窗口: 0
2025-07-30 16:07:59,768 - __main__ - INFO -   👻 隐藏窗口: 0
2025-07-30 16:07:59,769 - __main__ - INFO -   ❌ 无效窗口: 0
2025-07-30 16:07:59,769 - __main__ - INFO - ✅ 最终可用微信窗口列表:
2025-07-30 16:07:59,769 - __main__ - INFO -   🔥 窗口 1: 微信 (句柄: 2426918)
2025-07-30 16:07:59,771 - __main__ - INFO - 🔄 完成第 2 轮窗口循环，重新开始下一轮
2025-07-30 16:07:59,771 - __main__ - INFO - 📊 当前进度：已处理 4/2826 个联系人（剩余 2826 个）
2025-07-30 16:07:59,772 - __main__ - INFO - 
============================================================
2025-07-30 16:07:59,772 - __main__ - INFO - 🎯 开始处理第 1/1 个微信窗口 (第 3 轮)
2025-07-30 16:07:59,772 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 2426918)
2025-07-30 16:07:59,773 - __main__ - INFO - 📊 全局进度：已处理 4/2826 个联系人（剩余 2826 个）
2025-07-30 16:07:59,774 - __main__ - INFO - ============================================================
2025-07-30 16:07:59,774 - __main__ - INFO - 🚀 开始处理微信窗口 1
2025-07-30 16:07:59,774 - __main__ - INFO - 📋 窗口信息: 微信 (句柄: 2426918)
2025-07-30 16:07:59,776 - __main__ - INFO - 📍 当前执行步骤: WINDOW_MANAGEMENT (步骤 1)
2025-07-30 16:07:59,777 - __main__ - INFO - 🔧 步骤1：窗口管理 - 窗口 1
2025-07-30 16:07:59,777 - __main__ - INFO - 🔍 检查目标窗口状态: 微信 (句柄: 2426918)
2025-07-30 16:07:59,779 - __main__ - INFO - 🎯 激活目标窗口: 微信
2025-07-30 16:08:00,881 - __main__ - INFO - 🔝 设置窗口置顶: 微信
2025-07-30 16:08:00,882 - modules.window_manager - INFO - 🔝 确保窗口在最前面: 2426918
2025-07-30 16:08:00,882 - modules.window_manager - INFO - 🎯 激活微信窗口 (句柄: 2426918) - 增强版
2025-07-30 16:08:01,187 - modules.window_manager - INFO - ✅ 激活方法 1 成功
2025-07-30 16:08:01,187 - modules.window_manager - INFO - ✅ 微信窗口激活成功
2025-07-30 16:08:01,188 - modules.window_manager - INFO - ✅ 智能识别为微信主窗口: 726x650, 面积=471900
2025-07-30 16:08:01,188 - modules.window_manager - INFO - 🏠 检测到微信主窗口，执行大小调整和移动操作...
2025-07-30 16:08:01,188 - modules.window_manager - INFO - 🔧 调整微信主窗口大小为 723x650 像素...
2025-07-30 16:08:01,189 - modules.window_manager - INFO - 🔧 开始调整微信主窗口大小为 723x650 像素，位置为 (0, 0)...
2025-07-30 16:08:01,190 - modules.window_manager - INFO - 📏 当前窗口位置: (0, 0), 大小: 726x650
2025-07-30 16:08:01,190 - modules.window_manager - INFO - 🎯 目标位置: (0, 0), 目标大小: 723x650
2025-07-30 16:08:01,190 - modules.window_manager - INFO - ✅ 窗口已经是目标大小和位置，无需调整
2025-07-30 16:08:01,191 - modules.window_manager - INFO - ✅ 微信主窗口大小调整成功
2025-07-30 16:08:01,393 - modules.window_manager - INFO - ✅ 窗口已成功置于最前台
2025-07-30 16:08:01,393 - modules.window_manager - INFO - ✅ 窗口激活、管理和置顶验证成功
2025-07-30 16:08:01,395 - modules.window_manager - INFO - ✅ 窗口已设置为置顶: 2426918 (API返回: None)
2025-07-30 16:08:01,695 - modules.window_manager - INFO - ✅ 窗口成功置于最前面
2025-07-30 16:08:01,696 - __main__ - INFO - ✅ 步骤1完成：窗口 微信 已成功激活并置顶
2025-07-30 16:08:01,696 - __main__ - INFO - ✅ 步骤 1 执行成功
2025-07-30 16:08:02,697 - __main__ - INFO - 📍 当前执行步骤: MAIN_INTERFACE (步骤 2)
2025-07-30 16:08:02,697 - __main__ - INFO - 🖱️ 步骤2：主界面操作 - 窗口 1
2025-07-30 16:08:02,697 - __main__ - INFO - 🔄 主界面操作尝试 1/3
2025-07-30 16:08:02,698 - modules.main_interface - INFO - 🚀 [主界面操作] 开始执行微信主界面操作流程...
2025-07-30 16:08:02,698 - modules.main_interface - INFO - 🔍 [主界面操作] 验证微信窗口状态...
2025-07-30 16:08:02,698 - modules.main_interface - INFO - ✅ 当前前台窗口已是微信窗口: '微信' (类名: Qt51514QWindowIcon)
2025-07-30 16:08:02,699 - modules.main_interface - INFO - ✅ 微信窗口状态验证通过（使用main.py预激活的窗口）
2025-07-30 16:08:02,699 - modules.main_interface - INFO - ✅ [主界面操作] 微信窗口验证成功
2025-07-30 16:08:02,699 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤1: 点击微信按钮 (1/5)
2025-07-30 16:08:02,900 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤1: 点击微信按钮...
2025-07-30 16:08:02,901 - modules.main_interface - INFO - 🖱️ 点击 微信按钮: (31, 95)
2025-07-30 16:08:02,901 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 微信按钮 (31, 95)
2025-07-30 16:08:05,279 - modules.main_interface - INFO - ✅ 成功点击: 微信按钮
2025-07-30 16:08:05,279 - modules.main_interface - INFO - ✅ [主界面操作] 步骤1: 点击微信按钮 执行成功
2025-07-30 16:08:05,280 - modules.main_interface - INFO - ⏳ [主界面操作] 等待 2.5 秒...
2025-07-30 16:08:07,798 - modules.main_interface - INFO - 📋 [主界面操作] 执行 步骤2: 点击通讯录按钮 (2/5)
2025-07-30 16:08:08,051 - modules.main_interface - INFO - 🔄 [主界面操作] 开始执行 步骤2: 点击通讯录按钮...
2025-07-30 16:08:08,052 - modules.main_interface - INFO - 🖱️ 点击 通讯录按钮: (29, 144)
2025-07-30 16:08:08,053 - modules.main_interface - INFO - 🔴 高亮显示点击区域: 通讯录按钮 (29, 144)
