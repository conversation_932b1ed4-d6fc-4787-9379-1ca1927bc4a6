#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信自动化添加好友主控制程序
功能：协调执行完整的微信好友添加流程，支持多窗口循环处理

核心特性：
1. 严格的6步骤顺序执行：窗口管理→主界面→简单添加→图像识别→好友申请→频率处理
2. 单窗口完整性：每个微信窗口必须完成全部6个步骤后才能切换到下一个窗口
3. 循环处理机制：支持多个微信窗口的循环处理
4. 强制置顶：每完成一个步骤后强制置顶当前操作的微信窗口
5. 智能错误处理：异常捕获、自动重试、详细日志记录
6. 实时进度跟踪：显示当前处理的微信窗口和执行步骤
7. 配置和数据管理：从Excel读取联系人信息，支持参数配置
8. 结果统计：统计成功添加、失败、跳过的好友数量

版本：1.0.0
作者：AI助手
创建时间：2025-01-28
"""

import sys
import time
import logging
import traceback
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Union
from enum import Enum
from pathlib import Path

# 导入核心模块
from modules import (
    DataManager,
    WeChatWindowManager,
    WeChatMainInterface,
    WeChatAutoAddFriend,
    FriendRequestProcessor,
    FrequencyErrorHandler,
    ConfigManager
)

# 导入步骤执行器
from step_executor import StepExecutor

class ExecutionStep(Enum):
    """执行步骤枚举"""
    WINDOW_MANAGEMENT = 1    # 步骤1：窗口管理
    MAIN_INTERFACE = 2       # 步骤2：主界面操作
    SIMPLE_ADD = 3          # 步骤3：简单添加好友
    IMAGE_RECOGNITION = 4    # 步骤4：图像识别添加
    FRIEND_REQUEST = 5       # 步骤5：好友申请窗口
    FREQUENCY_HANDLING = 6   # 步骤6：频率限制处理

class WindowStatus(Enum):
    """窗口状态枚举"""
    PENDING = "待处理"
    PROCESSING = "处理中"
    COMPLETED = "已完成"
    ERROR = "错误"

class WeChatMainController:
    """微信自动化添加好友主控制器"""
    
    def __init__(self, excel_file: str = "添加好友名单.xlsx", config_file: str = "config.json"):
        """初始化主控制器"""
        # 设置日志
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # 配置文件
        self.excel_file = excel_file
        self.config_file = config_file
        
        # 初始化核心组件
        self.config_manager = ConfigManager(config_file)
        self.data_manager = DataManager(excel_file)
        self.frequency_handler = FrequencyErrorHandler()  # 🆕 先初始化频率错误处理器
        self.window_manager = WeChatWindowManager(frequency_handler=self.frequency_handler)  # 🆕 传入频率错误处理器引用
        self.main_interface = WeChatMainInterface(config_file, self.window_manager)
        self.auto_add_friend = WeChatAutoAddFriend()
        self.friend_request_processor = FriendRequestProcessor(config_file)
        self.step_executor = StepExecutor(excel_file)

        # 🆕 建立双向引用关系
        self.window_manager.set_frequency_handler(self.frequency_handler)

        # 🆕 启用智能检测功能
        self._enable_smart_detection()
        
        # 执行状态
        self.current_window_index = 0
        self.current_step = ExecutionStep.WINDOW_MANAGEMENT
        self.window_statuses: Dict[int, WindowStatus] = {}
        self._restart_required = False  # 重新开始标志
        self.execution_stats = {
            "total_contacts": 0,
            "processed_contacts": 0,
            "successful_adds": 0,
            "failed_adds": 0,
            "skipped_contacts": 0,
            "total_windows": 0,
            "completed_windows": 0,
            "start_time": None,
            "end_time": None
        }
        
        # 获取北京时间（修复：使用正确的北京时区）
        beijing_tz = timezone(timedelta(hours=8))
        self.beijing_time = datetime.now(beijing_tz).strftime('%Y-%m-%d %H:%M:%S')
        
        self.logger.info("✅ 微信自动化主控制器初始化完成")
        self.logger.info(f"📅 当前北京时间: {self.beijing_time}")
        
    def setup_logging(self):
        """设置日志配置"""
        log_dir = Path("logs/current")
        log_dir.mkdir(parents=True, exist_ok=True)
        
        log_file = log_dir / f"main_controller_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
    def _enable_smart_detection(self):
        """启用智能检测功能"""
        try:
            self.logger.info("🧠 启用智能检测功能...")

            # 从配置文件加载智能检测设置
            config = self.config_manager.get_config()
            smart_config = config.get('smart_detection', {})

            if smart_config.get('enabled', True):
                self.logger.info("✅ 智能检测功能已启用")
                self.logger.info("🔍 支持的检测方法:")

                detection_methods = smart_config.get('detection_methods', {})
                for method_name, method_config in detection_methods.items():
                    if method_config.get('enabled', True):
                        priority = method_config.get('priority', 0)
                        description = method_config.get('description', method_name)
                        self.logger.info(f"   {priority}. {description}")

                self.logger.info("🖱️ 支持的点击方法:")
                click_methods = smart_config.get('click_methods', {})
                for method_name, method_config in click_methods.items():
                    if method_config.get('enabled', True):
                        priority = method_config.get('priority', 0)
                        description = method_config.get('description', method_name)
                        self.logger.info(f"   {priority}. {description}")

                # 设置重试参数
                retry_settings = smart_config.get('retry_settings', {})
                max_detection_attempts = retry_settings.get('max_detection_attempts', 3)
                max_click_attempts = retry_settings.get('max_click_attempts', 4)

                self.logger.info(f"🔄 重试设置: 检测最多{max_detection_attempts}次, 点击最多{max_click_attempts}次")

                # 兼容性设置
                compatibility = smart_config.get('compatibility', {})
                if compatibility.get('cross_resolution', True):
                    self.logger.info("📱 已启用跨分辨率兼容性")
                if compatibility.get('window_position_independent', True):
                    self.logger.info("🪟 已启用窗口位置无关性")

            else:
                self.logger.warning("⚠️ 智能检测功能已禁用，将使用传统坐标点击")

        except Exception as e:
            self.logger.error(f"❌ 启用智能检测功能失败: {e}")
            self.logger.warning("⚠️ 将回退到传统检测方法")

    def get_wechat_windows(self) -> List[Dict]:
        """获取所有微信窗口（过滤黑名单窗口）- 增强版，实时更新黑名单"""
        try:
            self.logger.info("🔍 开始扫描微信窗口...")
            all_windows = self.window_manager.get_all_wechat_windows()

            if not all_windows:
                self.logger.error("❌ 未找到任何微信窗口")
                return []

            # 🆕 过滤黑名单窗口（增强版）
            valid_windows = []
            blacklisted_count = 0
            hidden_count = 0
            invalid_count = 0

            for window in all_windows:
                hwnd = window.get('hwnd')
                title = window.get('title', 'Unknown')

                # 🆕 验证hwnd有效性
                if not hwnd or not isinstance(hwnd, int):
                    self.logger.warning(f"⚠️ 跳过无效窗口句柄: {title} (句柄: {hwnd})")
                    invalid_count += 1
                    continue

                # 🆕 检查窗口是否仍然存在和可见
                import win32gui
                try:
                    if not win32gui.IsWindow(hwnd):
                        self.logger.warning(f"⚠️ 窗口已不存在: {title} (句柄: {hwnd})")
                        invalid_count += 1
                        continue

                    if not win32gui.IsWindowVisible(hwnd):
                        self.logger.warning(f"⚠️ 窗口已隐藏: {title} (句柄: {hwnd})")
                        hidden_count += 1
                        # 🆕 将隐藏的窗口也添加到黑名单
                        if hasattr(self, 'frequency_handler') and self.frequency_handler:
                            self.frequency_handler.add_window_to_blacklist(hwnd, title, "HIDDEN_WINDOW", "窗口已隐藏不可见")
                        continue
                except Exception as e:
                    self.logger.warning(f"⚠️ 检查窗口状态异常: {title} - {e}")
                    invalid_count += 1
                    continue

                # 🆕 检查黑名单状态
                if self.frequency_handler.is_window_blacklisted(hwnd, title):
                    blacklisted_count += 1
                    blacklist_info = self.frequency_handler.get_window_blacklist_info(hwnd, title)
                    self.logger.warning(f"🚫 跳过黑名单窗口: {title} (句柄: {hwnd})")
                    if blacklist_info:
                        self.logger.warning(f"   📋 错误类型: {blacklist_info.get('error_type', 'unknown')}")
                        self.logger.warning(f"   ⏰ 添加时间: {blacklist_info.get('added_time', 'unknown')}")
                else:
                    # 🆕 额外验证：检查窗口是否被最小化（可能是之前处理过的问题窗口）
                    try:
                        if win32gui.IsIconic(hwnd):
                            self.logger.warning(f"⚠️ 发现最小化窗口，可能是问题窗口: {title}")
                            # 将最小化的窗口也视为可疑，但不直接加入黑名单
                            # 而是标记为低优先级
                            window['priority'] = 'low'
                        else:
                            window['priority'] = 'normal'
                    except:
                        window['priority'] = 'normal'

                    valid_windows.append(window)

            # 🆕 详细统计信息
            total_found = len(all_windows)
            self.logger.info(f"📊 窗口扫描统计:")
            self.logger.info(f"  🔍 总发现窗口: {total_found}")
            self.logger.info(f"  ✅ 有效可用窗口: {len(valid_windows)}")
            self.logger.info(f"  🚫 黑名单窗口: {blacklisted_count}")
            self.logger.info(f"  👻 隐藏窗口: {hidden_count}")
            self.logger.info(f"  ❌ 无效窗口: {invalid_count}")

            if not valid_windows:
                self.logger.error("❌ 所有微信窗口都不可用（黑名单/隐藏/无效）")
                return []

            # 🆕 按优先级排序窗口（正常优先级的窗口排在前面）
            valid_windows.sort(key=lambda w: 0 if w.get('priority') == 'normal' else 1)

            self.logger.info(f"✅ 最终可用微信窗口列表:")
            for i, window in enumerate(valid_windows):
                priority_mark = "🔥" if window.get('priority') == 'normal' else "🔸"
                self.logger.info(f"  {priority_mark} 窗口 {i+1}: {window.get('title', 'Unknown')} (句柄: {window.get('hwnd', 'Unknown')})")
                self.window_statuses[i] = WindowStatus.PENDING

            self.execution_stats["total_windows"] = len(valid_windows)
            return valid_windows

        except Exception as e:
            self.logger.error(f"❌ 获取微信窗口失败: {e}")
            self.logger.error(traceback.format_exc())
            return []

    def move_all_windows_to_target_position(self, windows: List[Dict]) -> bool:
        """第一步就全部移动所有识别出的微信窗口到指定坐标位置（不激活窗口，只移动位置）"""
        try:
            self.logger.info("🎯 开始批量移动所有微信窗口到指定坐标位置...")
            self.logger.info(f"📊 需要移动的窗口数量: {len(windows)}")

            moved_count = 0
            failed_count = 0

            for i, window in enumerate(windows):
                hwnd = window.get("hwnd")
                title = window.get("title", "Unknown")

                if not hwnd:
                    self.logger.warning(f"⚠️ 窗口 {i+1} 句柄无效，跳过移动")
                    failed_count += 1
                    continue

                self.logger.info(f"🔄 移动窗口 {i+1}/{len(windows)}: {title} (句柄: {hwnd})")

                try:
                    # 🆕 直接移动窗口到指定位置，不激活窗口（避免重复移动）
                    if self._move_window_to_position_only(hwnd):
                        self.logger.info(f"✅ 窗口 {i+1} 移动成功")
                        moved_count += 1
                    else:
                        self.logger.warning(f"⚠️ 窗口 {i+1} 移动失败")
                        failed_count += 1

                    # 窗口间延迟，避免操作过快
                    if i < len(windows) - 1:  # 最后一个窗口不需要延迟
                        time.sleep(0.3)  # 🆕 减少延迟时间，提高效率

                except Exception as e:
                    self.logger.error(f"❌ 移动窗口 {i+1} 异常: {e}")
                    failed_count += 1

            # 统计结果
            self.logger.info(f"📊 批量窗口移动完成统计:")
            self.logger.info(f"  ✅ 成功移动: {moved_count} 个窗口")
            self.logger.info(f"  ❌ 移动失败: {failed_count} 个窗口")
            self.logger.info(f"  📈 成功率: {moved_count/len(windows)*100:.1f}%")

            if moved_count > 0:
                self.logger.info("🎉 批量窗口移动操作完成，所有窗口已移动到指定坐标位置")
                return True
            else:
                self.logger.error("❌ 所有窗口移动都失败了")
                return False

        except Exception as e:
            self.logger.error(f"❌ 批量移动窗口异常: {e}")
            self.logger.error(traceback.format_exc())
            return False

    def _move_window_to_position_only(self, hwnd: int) -> bool:
        """只移动窗口到指定位置，不激活窗口（避免重复移动操作）"""
        try:
            import win32gui
            import win32con

            # 检查窗口是否存在
            if not win32gui.IsWindow(hwnd):
                self.logger.warning(f"⚠️ 窗口句柄无效: {hwnd}")
                return False

            # 从配置获取目标位置
            config = self.window_manager.config
            target_position = config.get("window_position", [0, 0])
            target_x, target_y = target_position[0], target_position[1]

            # 获取窗口当前位置和大小
            rect = win32gui.GetWindowRect(hwnd)
            current_left, current_top, current_right, current_bottom = rect
            window_width = current_right - current_left
            window_height = current_bottom - current_top

            self.logger.debug(f"📏 当前窗口位置: ({current_left}, {current_top}), 大小: {window_width}x{window_height}")
            self.logger.debug(f"🎯 目标位置: ({target_x}, {target_y})")

            # 检查窗口是否已经在目标位置
            if abs(current_left - target_x) <= 10 and abs(current_top - target_y) <= 10:
                self.logger.debug(f"✅ 窗口已经在目标位置 ({target_x}, {target_y})，无需移动")
                return True

            # 直接移动窗口到目标位置（不改变大小）
            result = win32gui.SetWindowPos(
                hwnd,
                win32con.HWND_TOP,  # 不置顶，只是设置Z顺序
                target_x, target_y,
                0, 0,  # 不改变大小
                win32con.SWP_NOSIZE | win32con.SWP_NOZORDER  # 不改变大小和Z顺序
            )

            if result:
                # 验证移动是否成功
                time.sleep(0.1)  # 短暂等待
                new_rect = win32gui.GetWindowRect(hwnd)
                new_left, new_top = new_rect[0], new_rect[1]

                if abs(new_left - target_x) <= 10 and abs(new_top - target_y) <= 10:
                    self.logger.debug(f"✅ 窗口成功移动到 ({new_left}, {new_top})")
                    return True
                else:
                    self.logger.warning(f"⚠️ 窗口移动后位置不正确: 期望({target_x}, {target_y}), 实际({new_left}, {new_top})")
                    return False
            else:
                self.logger.warning("⚠️ SetWindowPos 调用失败")
                return False

        except Exception as e:
            self.logger.error(f"❌ 移动窗口到指定位置异常: {e}")
            return False

    def _activate_window_only(self, hwnd: int) -> bool:
        """优化的窗口激活方法 - 增强稳定性和错误处理"""
        try:
            import win32gui
            import win32con

            # 🆕 增强的窗口存在性检查
            if not hwnd or not isinstance(hwnd, int):
                self.logger.warning(f"⚠️ 窗口句柄无效或类型错误: {hwnd}")
                return False

            if not win32gui.IsWindow(hwnd):
                self.logger.warning(f"⚠️ 窗口不存在或已关闭: {hwnd}")
                return False

            # 🆕 获取窗口信息用于日志记录
            try:
                window_title = win32gui.GetWindowText(hwnd)
                self.logger.debug(f"🎯 激活窗口: {window_title} (句柄: {hwnd})")
            except:
                self.logger.debug(f"🎯 激活窗口: 句柄 {hwnd}")

            # 🆕 检查当前前台窗口，避免不必要的激活操作
            current_foreground = win32gui.GetForegroundWindow()
            if current_foreground == hwnd:
                self.logger.debug("✅ 目标窗口已经是前台窗口，无需激活")
                return True

            # 如果窗口最小化，先还原
            if win32gui.IsIconic(hwnd):
                self.logger.debug("🔄 窗口被最小化，正在还原...")
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                time.sleep(0.5)  # 🆕 增加等待时间确保还原完成

            # 确保窗口可见
            if not win32gui.IsWindowVisible(hwnd):
                self.logger.debug("🔄 窗口不可见，正在显示...")
                win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
                time.sleep(0.5)  # 🆕 增加等待时间确保显示完成

            # 🆕 优化的窗口激活策略
            activation_success = False

            # 方法1: 使用SetForegroundWindow（主要方法）
            try:
                self.logger.debug("   尝试方法1: SetForegroundWindow")
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.3)  # 🆕 增加等待时间

                # 验证激活是否成功
                current_hwnd = win32gui.GetForegroundWindow()
                if current_hwnd == hwnd:
                    self.logger.debug("✅ SetForegroundWindow 激活成功")
                    activation_success = True
                else:
                    self.logger.debug(f"⚠️ SetForegroundWindow 未完全成功 (当前前台: {current_hwnd})")

            except Exception as e:
                self.logger.warning(f"⚠️ SetForegroundWindow 异常: {e}")

            # 方法2: 如果方法1失败，尝试BringWindowToTop
            if not activation_success:
                try:
                    self.logger.debug("   尝试方法2: BringWindowToTop")
                    win32gui.BringWindowToTop(hwnd)
                    time.sleep(0.3)  # 🆕 增加等待时间

                    # 再次验证
                    current_hwnd = win32gui.GetForegroundWindow()
                    if current_hwnd == hwnd:
                        self.logger.debug("✅ BringWindowToTop 激活成功")
                        activation_success = True
                    else:
                        self.logger.debug(f"⚠️ BringWindowToTop 未完全成功 (当前前台: {current_hwnd})")

                except Exception as e:
                    self.logger.warning(f"⚠️ BringWindowToTop 异常: {e}")

            # 🆕 最终状态验证
            if activation_success:
                return True
            else:
                # 即使激活不完全成功，检查窗口是否至少可见且非最小化
                try:
                    is_visible = win32gui.IsWindowVisible(hwnd)
                    is_iconic = win32gui.IsIconic(hwnd)
                    if is_visible and not is_iconic:
                        self.logger.debug("✅ 窗口可见且非最小化（部分成功）")
                        return True
                    else:
                        self.logger.warning(f"❌ 窗口激活失败 (可见: {is_visible}, 最小化: {is_iconic})")
                        return False
                except Exception as e:
                    self.logger.warning(f"⚠️ 窗口状态检查异常: {e}")
                    return False

        except Exception as e:
            self.logger.error(f"❌ 激活窗口异常: {e}")
            return False

    def load_contacts_data(self) -> List[Dict]:
        """加载联系人数据"""
        try:
            self.logger.info("📂 开始加载联系人数据...")
            contacts = self.data_manager.load_phone_numbers()
            
            if not contacts:
                self.logger.error("❌ 未找到任何联系人数据")
                return []
            
            # 过滤待处理的联系人
            pending_contacts = [contact for contact in contacts 
                              if contact.get("status", "").strip() in ["", "待处理", "pending"]]
            
            self.logger.info(f"✅ 加载联系人数据完成")
            self.logger.info(f"📊 总联系人数: {len(contacts)}")
            self.logger.info(f"📋 待处理联系人数: {len(pending_contacts)}")
            
            self.execution_stats["total_contacts"] = len(pending_contacts)
            return pending_contacts
            
        except Exception as e:
            self.logger.error(f"❌ 加载联系人数据失败: {e}")
            self.logger.error(traceback.format_exc())
            return []
    
    def execute_step_1_window_management(self, window: Dict) -> bool:
        """执行步骤1：窗口管理（优化版 - 增强窗口状态检查和时序控制）"""
        try:
            self.logger.info(f"🔧 步骤1：窗口管理 - 窗口 {self.current_window_index + 1}")

            hwnd = window.get("hwnd")
            window_title = window.get("title", "Unknown")

            if not hwnd:
                self.logger.error("❌ 窗口句柄无效")
                return False

            # 🆕 增强的窗口状态检查
            self.logger.info(f"🔍 检查目标窗口状态: {window_title} (句柄: {hwnd})")

            # 检查窗口是否仍然存在
            import win32gui
            if not win32gui.IsWindow(hwnd):
                self.logger.error(f"❌ 目标窗口已不存在: {window_title}")
                return False

            # 🆕 检查是否有其他微信窗口在前台，如果有则先处理
            current_foreground = win32gui.GetForegroundWindow()
            if current_foreground != hwnd:
                try:
                    current_title = win32gui.GetWindowText(current_foreground)
                    if "微信" in current_title and current_foreground != hwnd:
                        self.logger.info(f"🔄 检测到其他微信窗口在前台: {current_title}")
                        self.logger.info("⏳ 等待前台窗口稳定后再激活目标窗口...")
                        time.sleep(1.0)  # 🆕 增加等待时间确保窗口状态稳定
                except:
                    pass

            # 🆕 激活目标窗口（使用优化的激活方法）
            self.logger.info(f"🎯 激活目标窗口: {window_title}")
            if not self._activate_window_only(hwnd):
                self.logger.error(f"❌ 激活窗口失败: {window_title}")
                return False

            # 🆕 增加激活后的稳定等待时间
            time.sleep(0.8)  # 确保窗口激活完全生效

            # 确保窗口置顶
            self.logger.info(f"🔝 设置窗口置顶: {window_title}")
            if not self.window_manager.ensure_window_on_top(hwnd):
                self.logger.warning("⚠️ 窗口置顶失败，但继续执行")

            # 🆕 最终验证窗口状态
            final_foreground = win32gui.GetForegroundWindow()
            if final_foreground == hwnd:
                self.logger.info(f"✅ 步骤1完成：窗口 {window_title} 已成功激活并置顶")
            else:
                self.logger.warning(f"⚠️ 窗口激活可能不完全，但继续执行 (当前前台: {final_foreground})")

            return True

        except Exception as e:
            self.logger.error(f"❌ 步骤1执行失败: {e}")
            self.logger.error(traceback.format_exc())
            return False
    
    def execute_step_2_main_interface(self, window: Dict) -> bool:
        """执行步骤2：主界面操作 - 增强版"""
        try:
            self.logger.info(f"🖱️ 步骤2：主界面操作 - 窗口 {self.current_window_index + 1}")

            # 增加重试机制
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    self.logger.info(f"🔄 主界面操作尝试 {attempt + 1}/{max_retries}")

                    # 执行主界面操作流程
                    result = self.main_interface.execute_main_interface_flow()

                    if result:
                        self.logger.info("✅ 主界面操作流程执行成功")
                        break
                    else:
                        self.logger.warning(f"⚠️ 主界面操作流程失败，尝试 {attempt + 1}/{max_retries}")
                        if attempt < max_retries - 1:
                            self.logger.info("🔄 等待2秒后重试...")
                            time.sleep(2)
                            continue
                        else:
                            self.logger.error("❌ 主界面操作流程重试后仍然失败")
                            return False

                except Exception as flow_error:
                    self.logger.error(f"❌ 主界面操作流程异常 (尝试 {attempt + 1}): {flow_error}")
                    if attempt < max_retries - 1:
                        self.logger.info("🔄 等待2秒后重试...")
                        time.sleep(2)
                        continue
                    else:
                        self.logger.error("❌ 主界面操作流程重试后仍然异常")
                        raise flow_error

            # 强制置顶窗口
            try:
                hwnd = window.get("hwnd")
                if hwnd:
                    self.window_manager.ensure_window_on_top(hwnd)
                    self.logger.info("✅ 窗口置顶操作完成")
            except Exception as top_error:
                self.logger.warning(f"⚠️ 窗口置顶操作失败，但继续执行: {top_error}")

            self.logger.info("✅ 步骤2：主界面操作完成")
            return True

        except Exception as e:
            self.logger.error(f"❌ 步骤2执行失败: {e}")
            self.logger.error(f"❌ 异常详情: {type(e).__name__}: {str(e)}")
            self.logger.error(traceback.format_exc())
            return False
    
    def execute_step_3_simple_add(self, window: Dict, contacts: List[Dict]) -> Union[bool, str]:
        """执行步骤3：简单添加好友"""
        try:
            self.logger.info(f"📞 步骤3：简单添加好友 - 窗口 {self.current_window_index + 1}")

            # 直接调用 wechat_auto_add_simple.py 的完整功能
            self.logger.info("� 调用 wechat_auto_add_simple.py 执行完整的添加好友流程...")

            # 导入并初始化 wechat_auto_add_simple 模块
            from modules.wechat_auto_add_simple import WeChatAutoAddFriend as SimpleAddModule

            # 创建简单添加好友实例
            simple_add_instance = SimpleAddModule(self.excel_file)

            # 🆕 传递全局联系人索引，确保从正确位置开始处理
            global_index = getattr(self, 'global_contact_index', 0)
            self.logger.info(f"🔄 传递全局联系人索引: {global_index}")

            # 执行完整的自动化流程（包含窗口移动功能）
            self.logger.info("🔧 执行包含窗口移动的完整自动化流程...")
            result = simple_add_instance.run_automation(start_index=global_index)

            # 检查是否需要重新开始
            if result == "RESTART_REQUIRED":
                self.logger.warning("🔄 检测到频率错误，需要重新开始流程")

                # 更新统计信息
                stats = simple_add_instance.stats
                self.execution_stats["processed_contacts"] += stats.get("processed", 0)
                self.execution_stats["successful_adds"] += stats.get("success", 0)
                self.execution_stats["failed_adds"] += stats.get("failed", 0)

                self.logger.info(f"📊 步骤3部分完成 - 处理: {stats.get('processed', 0)}, 成功: {stats.get('success', 0)}, 失败: {stats.get('failed', 0)}")
                self.logger.info("🔄 设置重新开始标志，将从第一步重新开始")

                # 设置重新开始标志
                self._restart_required = True
                return "RESTART_REQUIRED"  # 返回特殊状态码

            # 🆕 检查是否需要切换窗口（达到联系人数量限制）
            elif result == "WINDOW_SWITCH_REQUIRED":
                self.logger.info("🔄 检测到联系人数量限制，需要切换到下一个窗口")

                # 更新统计信息
                stats = simple_add_instance.stats
                self.execution_stats["processed_contacts"] += stats.get("processed", 0)
                self.execution_stats["successful_adds"] += stats.get("success", 0)
                self.execution_stats["failed_adds"] += stats.get("failed", 0)

                self.logger.info(f"📊 步骤3部分完成 - 处理: {stats.get('processed', 0)}, 成功: {stats.get('success', 0)}, 失败: {stats.get('failed', 0)}")
                self.logger.info("🔄 当前窗口联系人处理完成，准备切换到下一个窗口")

                return "WINDOW_SWITCH_REQUIRED"  # 返回特殊状态码

            elif result:
                self.logger.info("✅ wechat_auto_add_simple.py 执行成功")

                # 更新统计信息
                stats = simple_add_instance.stats
                self.execution_stats["processed_contacts"] += stats.get("processed", 0)
                self.execution_stats["successful_adds"] += stats.get("success", 0)
                self.execution_stats["failed_adds"] += stats.get("failed", 0)

                self.logger.info(f"📊 步骤3完成 - 处理: {stats.get('processed', 0)}, 成功: {stats.get('success', 0)}, 失败: {stats.get('failed', 0)}")
                return True
            else:
                self.logger.error("❌ wechat_auto_add_simple.py 执行失败")
                return False

        except Exception as e:
            self.logger.error(f"❌ 步骤3执行失败: {e}")
            self.logger.error(traceback.format_exc())
            return False
    
    def execute_step_4_image_recognition(self, window: Dict) -> bool:
        """执行步骤4：图像识别添加"""
        try:
            self.logger.info(f"🖼️ 步骤4：图像识别添加 - 窗口 {self.current_window_index + 1}")
            
            # 执行图像识别添加好友
            if not self.auto_add_friend.execute_auto_add_friend():
                self.logger.error("❌ 图像识别添加失败")
                return False
            
            # 强制置顶窗口
            hwnd = window.get("hwnd")
            if hwnd:
                self.window_manager.ensure_window_on_top(hwnd)
            
            self.logger.info("✅ 步骤4：图像识别添加完成")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 步骤4执行失败: {e}")
            self.logger.error(traceback.format_exc())
            return False
    
    def execute_step_5_friend_request(self, window: Dict, contact: Dict) -> bool:
        """执行步骤5：好友申请窗口"""
        try:
            self.logger.info(f"👥 步骤5：好友申请窗口 - 窗口 {self.current_window_index + 1}")
            
            phone = contact.get("phone", "")
            if not phone:
                self.logger.error("❌ 联系人手机号为空")
                return False
            
            # 执行好友申请流程
            result = self.friend_request_processor.execute_friend_request_flow(
                phone=phone,
                verification_msg=""  # 验证信息将从Excel中读取
            )
            
            if not result or (isinstance(result, dict) and result.get("status") != "success"):
                self.logger.error("❌ 好友申请流程失败")
                return False
            
            # 强制置顶窗口
            hwnd = window.get("hwnd")
            if hwnd:
                self.window_manager.ensure_window_on_top(hwnd)
            
            self.logger.info("✅ 步骤5：好友申请窗口完成")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 步骤5执行失败: {e}")
            self.logger.error(traceback.format_exc())
            return False
    
    def execute_step_6_frequency_handling(self, window: Dict) -> Union[bool, str]:
        """执行步骤6：频率限制处理（窗口独立判断版 - 只检测和处理当前窗口的频率错误）"""
        try:
            current_window_hwnd = window.get("hwnd")
            window_title = window.get("title", "Unknown")

            self.logger.info(f"⏱️ 步骤6：频率限制处理 - 窗口 {self.current_window_index + 1}")
            self.logger.info(f"🎯 当前检测窗口: {window_title} (句柄: {current_window_hwnd})")

            # 🆕 验证窗口句柄有效性
            if not current_window_hwnd or not isinstance(current_window_hwnd, int):
                self.logger.error(f"❌ 无效的窗口句柄: {current_window_hwnd}")
                return False

            # 🆕 针对当前特定窗口进行独立的频率错误检测
            self.logger.info(f"� 开始检测窗口 {window_title} 的频率错误...")
            detection_result = self._detect_frequency_error_for_specific_window(current_window_hwnd, window_title)

            if detection_result and detection_result.has_error:
                self.logger.warning(f"🚨 窗口 {window_title} 检测到频率限制错误！")
                self.logger.info(f"📋 错误详情: {detection_result.error_message}")
                self.logger.info(f"� 检测置信度: {detection_result.confidence:.2f}")

                # 🆕 获取window_manager识别的所有微信窗口列表（用于验证）
                managed_windows = self.window_manager.get_all_wechat_windows()
                self.logger.info(f"🔍 window_manager识别的微信窗口数量: {len(managed_windows)}")

                # 🆕 执行针对当前窗口的精确频率错误处理
                if not self._handle_frequency_error_for_current_window_only(detection_result, current_window_hwnd, managed_windows):
                    self.logger.error(f"❌ 窗口 {window_title} 频率限制错误处理失败")
                    return False

                self.logger.info(f"✅ 窗口 {window_title} 频率限制错误处理完成")
                self.logger.info(f"🔄 窗口 {window_title} 需要重新开始流程")

                # 返回特殊状态码，表示当前窗口需要重新开始
                return "RESTART_REQUIRED"
            else:
                self.logger.info(f"✅ 窗口 {window_title} 未检测到频率限制错误，继续正常流程")

            # 如果没有频率错误，尝试强制置顶窗口
            hwnd = window.get("hwnd")
            if hwnd:
                try:
                    self.window_manager.ensure_window_on_top(hwnd)
                except Exception as e:
                    self.logger.warning(f"⚠️ 窗口置顶失败: {e}")

            self.logger.info("✅ 步骤6：频率限制处理完成")
            return True

        except Exception as e:
            self.logger.error(f"❌ 步骤6执行失败: {e}")
            self.logger.error(traceback.format_exc())
            return False

    def _detect_frequency_error_for_specific_window(self, window_hwnd: int, window_title: str):
        """针对特定窗口进行独立的频率错误检测"""
        try:
            self.logger.info(f"🔍 开始检测窗口 {window_title} (句柄: {window_hwnd}) 的频率错误...")

            # 🆕 使用窗口特定的检测逻辑
            detection_result = self.frequency_handler.detect_frequency_error_for_window(window_hwnd, window_title)

            if detection_result and detection_result.has_error:
                self.logger.info(f"🚨 窗口 {window_title} 检测到频率错误")
                self.logger.info(f"📋 错误类型: {detection_result.error_type}")
                self.logger.info(f"📝 错误信息: {detection_result.error_message}")
                self.logger.info(f"📊 置信度: {detection_result.confidence:.2f}")
            else:
                self.logger.info(f"✅ 窗口 {window_title} 未检测到频率错误")

            return detection_result

        except Exception as e:
            self.logger.error(f"❌ 检测窗口 {window_title} 频率错误异常: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return None

    def _handle_frequency_error_for_current_window_only(self, detection_result, current_window_hwnd: int, managed_windows: List[Dict]) -> bool:
        """只处理当前窗口的频率错误 - 不影响其他窗口（安全增强版）"""
        try:
            window_title = "Unknown"

            # 获取窗口标题
            for managed_window in managed_windows:
                if managed_window.get("hwnd") == current_window_hwnd:
                    window_title = managed_window.get('title', 'Unknown')
                    break

            self.logger.info(f"🎯 开始安全处理窗口 {window_title} 的频率错误...")

            # 🆕 增强安全验证：确保窗口确实存在且有效
            if not current_window_hwnd or not isinstance(current_window_hwnd, int):
                self.logger.error(f"❌ 无效的窗口句柄: {current_window_hwnd}")
                return False

            # 验证窗口是否仍然存在
            try:
                import win32gui
                if not win32gui.IsWindow(current_window_hwnd):
                    self.logger.warning(f"⚠️ 窗口 {window_title} 已不存在，跳过处理")
                    return True  # 窗口已关闭，认为处理成功
            except Exception as e:
                self.logger.warning(f"⚠️ 无法验证窗口存在性: {e}")

            # 验证当前窗口是否在managed_windows列表中
            current_window_found = False
            for managed_window in managed_windows:
                if managed_window.get("hwnd") == current_window_hwnd:
                    current_window_found = True
                    self.logger.info(f"✅ 确认窗口 {window_title} 在管理列表中")
                    break

            if not current_window_found:
                self.logger.warning(f"⚠️ 窗口 {window_title} (句柄: {current_window_hwnd}) 不在window_manager管理列表中")
                self.logger.warning("🔒 为安全起见，不执行任何关闭操作")
                return False

            # 🆕 只处理当前出现错误的特定微信窗口
            self.logger.info(f"🎯 只处理窗口 {window_title} 的频率错误，不影响其他窗口")
            self.logger.info("🔒 使用安全模式：优先温和关闭，避免强制进程终止")

            # 步骤1: 使用智能检测点击错误对话框的确定按钮
            self.logger.info("🔄 步骤1: 使用智能检测点击错误对话框确定按钮...")
            self.logger.info("🧠 智能检测将尝试多种方法自动定位和点击确定按钮")
            try:
                success = self.frequency_handler._click_error_dialog_ok_button(detection_result)
                if success:
                    self.logger.info("✅ 智能检测成功处理错误对话框")
                else:
                    self.logger.warning("⚠️ 智能检测处理对话框失败，但流程继续")
                time.sleep(1.0)  # 等待对话框关闭
            except Exception as e:
                self.logger.warning(f"⚠️ 智能检测点击错误对话框异常: {e}")
                self.logger.info("🔄 智能检测将自动尝试其他方法...")

            # 步骤2: 安全关闭当前微信窗口的添加朋友窗口
            self.logger.info(f"🔄 步骤2: 安全关闭窗口 {window_title} 的添加朋友窗口...")
            try:
                self._safe_close_add_friend_window_for_current_wechat(current_window_hwnd)
            except Exception as e:
                self.logger.warning(f"⚠️ 关闭添加朋友窗口失败: {e}")

            # 步骤3: 安全关闭当前微信主窗口
            self.logger.info(f"🔄 步骤3: 安全关闭窗口 {window_title} 的主窗口...")
            try:
                self._safe_close_current_wechat_main_window(current_window_hwnd)
            except Exception as e:
                self.logger.warning(f"⚠️ 关闭微信主窗口失败: {e}")

            self.logger.info(f"✅ 窗口 {window_title} 频率错误处理完成")
            self.logger.info(f"💡 其他微信窗口不受影响，继续正常运行")
            self.logger.info("🔒 已使用安全关闭方法，避免了批量窗口关闭问题")
            return True

        except Exception as e:
            self.logger.error(f"❌ 处理窗口频率错误异常: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False

    def _close_add_friend_window_for_current_wechat(self, main_window_hwnd: int) -> bool:
        """关闭指定微信主窗口相关的添加朋友窗口"""
        try:
            self.logger.debug(f"🔍 为微信窗口 {main_window_hwnd} 查找相关的添加朋友窗口")

            # 查找与当前微信窗口相关的添加朋友窗口
            add_friend_hwnd = self.window_manager.find_window_by_title("添加朋友")
            if add_friend_hwnd:
                self.logger.info(f"🔄 找到添加朋友窗口: {add_friend_hwnd}")
                self.window_manager.close_window(add_friend_hwnd)
                time.sleep(0.5)
                return True
            else:
                self.logger.info("ℹ️ 未找到添加朋友窗口")
                return True
        except Exception as e:
            self.logger.warning(f"⚠️ 关闭添加朋友窗口异常: {e}")
            return False

    def _close_current_wechat_main_window(self, window_hwnd: int) -> bool:
        """关闭指定的微信主窗口"""
        try:
            self.logger.info(f"🔄 关闭微信主窗口: {window_hwnd}")
            self.window_manager.close_window(window_hwnd)
            time.sleep(1.0)
            return True
        except Exception as e:
            self.logger.warning(f"⚠️ 关闭微信主窗口异常: {e}")
            return False

    def _safe_close_add_friend_window_for_current_wechat(self, main_window_hwnd: int) -> bool:
        """安全关闭指定微信主窗口相关的添加朋友窗口"""
        try:
            self.logger.debug(f"🔍 为微信窗口 {main_window_hwnd} 安全查找相关的添加朋友窗口")

            # 查找与当前微信窗口相关的添加朋友窗口
            add_friend_hwnd = self.window_manager.find_window_by_title("添加朋友")
            if add_friend_hwnd:
                self.logger.info(f"🔄 找到添加朋友窗口: {add_friend_hwnd}")

                # 🆕 使用安全的关闭方法
                try:
                    import win32gui
                    import win32con

                    # 验证窗口仍然存在
                    if win32gui.IsWindow(add_friend_hwnd):
                        # 使用温和的关闭方法
                        win32gui.PostMessage(add_friend_hwnd, win32con.WM_CLOSE, 0, 0)
                        time.sleep(0.5)

                        # 验证是否成功关闭
                        if not win32gui.IsWindow(add_friend_hwnd) or not win32gui.IsWindowVisible(add_friend_hwnd):
                            self.logger.info("✅ 添加朋友窗口已安全关闭")
                            return True
                        else:
                            self.logger.warning("⚠️ 添加朋友窗口未完全关闭，但继续执行")
                            return True
                    else:
                        self.logger.info("ℹ️ 添加朋友窗口已不存在")
                        return True

                except Exception as e:
                    self.logger.warning(f"⚠️ 安全关闭添加朋友窗口异常: {e}")
                    return True  # 继续执行，不阻断流程
            else:
                self.logger.info("ℹ️ 未找到添加朋友窗口")
                return True

        except Exception as e:
            self.logger.warning(f"⚠️ 安全关闭添加朋友窗口异常: {e}")
            return True  # 继续执行，不阻断流程

    def _safe_close_current_wechat_main_window(self, window_hwnd: int) -> bool:
        """强制关闭指定的微信主窗口 - 增强版，确保窗口真正关闭"""
        try:
            self.logger.info(f"🔄 强制关闭微信主窗口: {window_hwnd}")

            import win32gui
            import win32con
            import win32process
            import psutil

            # 验证窗口仍然存在
            if not win32gui.IsWindow(window_hwnd):
                self.logger.info("ℹ️ 微信主窗口已不存在")
                return True

            # 获取窗口信息用于日志
            try:
                window_title = win32gui.GetWindowText(window_hwnd)
                self.logger.info(f"🎯 目标窗口: {window_title}")
            except:
                window_title = "Unknown"

            # 🆕 方法1: 使用频率错误处理器的强化关闭方法
            self.logger.info("   方法1: 使用频率错误处理器的强化关闭方法...")
            try:
                if hasattr(self, 'frequency_handler') and self.frequency_handler:
                    if self.frequency_handler._close_window(window_hwnd):
                        self.logger.info("✅ 频率错误处理器成功关闭窗口")
                        return True
            except Exception as e:
                self.logger.warning(f"⚠️ 频率错误处理器关闭失败: {e}")

            # 🆕 方法2: 多重API关闭方法
            self.logger.info("   方法2: 多重API关闭方法...")

            # 2.1: WM_CLOSE消息
            win32gui.PostMessage(window_hwnd, win32con.WM_CLOSE, 0, 0)
            time.sleep(1.5)
            if not win32gui.IsWindow(window_hwnd) or not win32gui.IsWindowVisible(window_hwnd):
                self.logger.info("✅ WM_CLOSE成功关闭窗口")
                return True

            # 2.2: 系统关闭命令
            win32gui.PostMessage(window_hwnd, win32con.WM_SYSCOMMAND, win32con.SC_CLOSE, 0)
            time.sleep(1.5)
            if not win32gui.IsWindow(window_hwnd) or not win32gui.IsWindowVisible(window_hwnd):
                self.logger.info("✅ 系统关闭命令成功关闭窗口")
                return True

            # 🆕 方法3: 强制隐藏窗口（如果无法关闭，至少让它不可见）
            self.logger.info("   方法3: 强制隐藏窗口...")
            try:
                win32gui.ShowWindow(window_hwnd, win32con.SW_HIDE)
                time.sleep(0.5)

                # 验证窗口是否已隐藏
                if not win32gui.IsWindowVisible(window_hwnd):
                    self.logger.info("✅ 窗口已强制隐藏")
                    # 🆕 将隐藏的窗口添加到黑名单，防止后续激活
                    if hasattr(self, 'frequency_handler') and self.frequency_handler:
                        self.frequency_handler.add_window_to_blacklist(window_hwnd, window_title, "FORCE_HIDDEN", "窗口已被强制隐藏")
                    return True
            except Exception as e:
                self.logger.warning(f"⚠️ 强制隐藏窗口失败: {e}")

            # 🆕 方法4: 获取进程信息，记录但不强制终止进程（避免影响其他微信窗口）
            self.logger.info("   方法4: 记录进程信息，标记为问题窗口...")
            try:
                _, process_id = win32process.GetWindowThreadProcessId(window_hwnd)
                process = psutil.Process(process_id)
                self.logger.warning(f"⚠️ 顽固窗口信息 - 进程: {process.name()}, PID: {process_id}")

                # 🆕 将顽固窗口添加到黑名单
                if hasattr(self, 'frequency_handler') and self.frequency_handler:
                    self.frequency_handler.add_window_to_blacklist(window_hwnd, window_title, "STUBBORN_WINDOW", "窗口无法正常关闭")
                    self.logger.info("� 已将顽固窗口添加到黑名单，后续将跳过此窗口")

                # 🆕 最后尝试最小化窗口
                win32gui.ShowWindow(window_hwnd, win32con.SW_MINIMIZE)
                self.logger.info("📉 已将顽固窗口最小化")

                return True  # 虽然没有完全关闭，但已经处理了

            except Exception as e:
                self.logger.warning(f"⚠️ 获取进程信息失败: {e}")

            # 如果所有方法都失败
            self.logger.error(f"❌ 所有关闭方法都失败，窗口 {window_title} 可能需要手动处理")

            # 🆕 即使失败也要添加到黑名单
            if hasattr(self, 'frequency_handler') and self.frequency_handler:
                self.frequency_handler.add_window_to_blacklist(window_hwnd, window_title, "CLOSE_FAILED", "所有关闭方法都失败")
                self.logger.info("🚫 已将无法关闭的窗口添加到黑名单")

            return False  # 🆕 返回False表示关闭失败

        except Exception as e:
            self.logger.error(f"❌ 强制关闭微信主窗口异常: {e}")
            return False

    def execute_single_window_flow(self, window: Dict, contacts: List[Dict]) -> Union[bool, str]:
        """执行单个微信窗口的完整6步骤流程"""
        try:
            self.logger.info(f"🚀 开始处理微信窗口 {self.current_window_index + 1}")
            self.logger.info(f"📋 窗口信息: {window.get('title', 'Unknown')} (句柄: {window.get('hwnd', 'Unknown')})")

            # 更新窗口状态
            self.window_statuses[self.current_window_index] = WindowStatus.PROCESSING

            # 严格按照1→2→3→4→5→6的顺序执行
            steps = [
                (ExecutionStep.WINDOW_MANAGEMENT, self.execute_step_1_window_management, [window]),
                (ExecutionStep.MAIN_INTERFACE, self.execute_step_2_main_interface, [window]),
                (ExecutionStep.SIMPLE_ADD, self.execute_step_3_simple_add, [window, contacts]),
                (ExecutionStep.IMAGE_RECOGNITION, self.execute_step_4_image_recognition, [window]),
                (ExecutionStep.FRIEND_REQUEST, self.execute_step_5_friend_request, [window, contacts[0] if contacts else {}]),
                (ExecutionStep.FREQUENCY_HANDLING, self.execute_step_6_frequency_handling, [window])
            ]

            # 逐步执行
            for step_enum, step_func, step_args in steps:
                self.current_step = step_enum
                self.logger.info(f"📍 当前执行步骤: {step_enum.name} (步骤 {step_enum.value})")

                # 执行步骤
                try:
                    result = step_func(*step_args)

                    # 🆕 强化频率错误检测：检查是否需要立即切换窗口
                    if result == "RESTART_REQUIRED":
                        self.logger.warning(f"🔄 步骤 {step_enum.value} 检测到频率错误，需要立即切换窗口")
                        self.logger.info("🚪 频率错误处理完成，当前微信窗口已强制关闭")
                        self.logger.info("🔄 立即终止当前窗口的所有后续步骤")
                        self.logger.info("🔄 准备切换到下一个微信窗口并重新开始完整流程")

                        # 🆕 标记当前窗口为频率错误状态（而非处理中）
                        self.window_statuses[self.current_window_index] = WindowStatus.ERROR

                        # 🆕 立即返回，不再执行后续步骤
                        return "RESTART_REQUIRED"  # 返回特殊状态码

                    # 🆕 检查是否需要切换窗口（联系人数量限制）
                    elif result == "WINDOW_SWITCH_REQUIRED":
                        self.logger.info(f"🔄 步骤 {step_enum.value} 达到联系人处理数量限制，需要切换窗口")
                        self.logger.info("📋 当前窗口联系人处理完成，立即终止当前窗口的所有后续步骤")
                        self.logger.info("🔄 准备切换到下一个微信窗口并重新开始完整流程")

                        # 🆕 标记当前窗口为部分完成状态
                        self.window_statuses[self.current_window_index] = WindowStatus.COMPLETED

                        # 🆕 立即返回，不再执行后续步骤
                        return "WINDOW_SWITCH_REQUIRED"  # 返回特殊状态码

                    if not result:
                        self.logger.error(f"❌ 步骤 {step_enum.value} 执行失败，终止当前窗口处理")
                        self.window_statuses[self.current_window_index] = WindowStatus.ERROR
                        return False

                    # 🆕 在每个步骤完成后检查是否需要立即停止
                    self.logger.info(f"✅ 步骤 {step_enum.value} 执行成功")

                    # 🆕 特别处理步骤3：如果步骤3完成且没有频率错误，继续后续步骤
                    if step_enum == ExecutionStep.SIMPLE_ADD:
                        self.logger.info("📋 步骤3（简单添加好友）完成，检查是否需要继续后续步骤...")
                        # 这里不需要额外检查，因为如果有频率错误，上面已经返回了

                    # 🆕 特别处理步骤6：频率错误处理步骤也可能触发窗口切换
                    if step_enum == ExecutionStep.FREQUENCY_HANDLING:
                        self.logger.info("📋 步骤6（频率错误处理）完成")
                        if result == "RESTART_REQUIRED":
                            self.logger.warning("🚨 步骤6检测到频率错误，所有微信窗口已强制关闭")
                            # 上面的逻辑已经处理了这种情况，这里只是额外日志

                    # 步骤间延迟
                    time.sleep(1)

                except Exception as e:
                    self.logger.error(f"❌ 步骤 {step_enum.value} 执行异常: {e}")
                    self.logger.error(traceback.format_exc())
                    self.window_statuses[self.current_window_index] = WindowStatus.ERROR
                    return False

            # 所有步骤完成
            self.window_statuses[self.current_window_index] = WindowStatus.COMPLETED
            self.execution_stats["completed_windows"] += 1

            self.logger.info(f"✅ 微信窗口 {self.current_window_index + 1} 的6步骤流程全部完成")
            return True

        except Exception as e:
            self.logger.error(f"❌ 微信窗口 {self.current_window_index + 1} 处理异常: {e}")
            self.logger.error(traceback.format_exc())
            self.window_statuses[self.current_window_index] = WindowStatus.ERROR
            return False

    def execute_multi_window_flow(self, windows: List[Dict], contacts: List[Dict]) -> bool:
        """执行多微信窗口循环处理流程"""
        try:
            self.logger.info("🔄 开始多微信窗口循环处理")
            self.logger.info(f"📊 总窗口数: {len(windows)}, 总联系人数: {len(contacts)}")

            # 🆕 设置频率错误处理器的总窗口数量
            if hasattr(self, 'frequency_handler') and self.frequency_handler:
                self.frequency_handler.set_total_windows_count(len(windows))

            # 🆕 添加全局联系人处理统计
            total_processed = 0
            total_contacts = len(contacts)

            # 🆕 添加全局联系人索引跟踪
            self.global_contact_index = 0  # 全局联系人处理索引

            # 🆕 循环处理所有联系人，直到全部处理完毕
            window_index = 0
            cycle_count = 0

            while total_processed < total_contacts:
                # 🆕 强化终止检查：检查微信窗口数量和终止标志
                if hasattr(self, 'frequency_handler'):
                    # 检查微信窗口数量
                    current_window_count = len(self.get_wechat_windows())
                    self.logger.debug(f"📊 循环开始检查: 当前微信窗口数量={current_window_count}")

                    # 如果没有微信窗口，立即终止
                    if current_window_count == 0:
                        self.logger.error("🚨 检测到系统中没有微信窗口")
                        self.logger.error("🚨 所有微信窗口都已关闭，程序立即终止")
                        self.logger.error(f"📊 最终统计：已处理 {total_processed}/{total_contacts} 个联系人")
                        return False

                    # 检查终止标志
                    if self.frequency_handler.is_terminate_required():
                        termination_reason = self.frequency_handler.get_termination_reason()
                        self.logger.error("🚨 检测到程序终止标志")
                        self.logger.error(f"🚨 终止原因: {termination_reason}")
                        self.logger.error("🚨 程序将立即终止，无法继续处理联系人")
                        self.logger.error(f"📊 最终统计：已处理 {total_processed}/{total_contacts} 个联系人")

                        # 显示窗口错误状态（如果有）
                        if hasattr(self.frequency_handler, 'get_window_error_status'):
                            window_status = self.frequency_handler.get_window_error_status()
                            if window_status:
                                self.logger.error("📊 窗口错误状态统计:")
                                for window_hwnd, status in window_status.items():
                                    self.logger.error(f"  窗口 {status['title']} (句柄:{window_hwnd}): {status['error_count']} 次错误")

                        return False

                # 🆕 检查是否所有窗口都已被加入黑名单
                available_windows = []
                blacklisted_windows = []
                for window in windows:
                    hwnd = window.get('hwnd')
                    title = window.get('title', 'Unknown')
                    # 确保hwnd是有效的整数
                    if hwnd is not None and isinstance(hwnd, int):
                        if hasattr(self, 'frequency_handler') and self.frequency_handler.is_window_blacklisted(hwnd, title):
                            blacklisted_windows.append(f"{title} (句柄:{hwnd})")
                        else:
                            available_windows.append(window)
                    else:
                        # 如果hwnd无效，跳过此窗口
                        self.logger.warning(f"⚠️ 窗口句柄无效，跳过: {title} (句柄:{hwnd})")
                        continue

                # 如果所有窗口都被加入黑名单，立即终止程序
                if not available_windows:
                    self.logger.error("🚨 所有微信窗口都已被加入黑名单，程序无法继续")
                    self.logger.error("🚨 程序将立即终止，无法继续处理联系人")
                    self.logger.error(f"📊 最终统计：已处理 {total_processed}/{total_contacts} 个联系人")
                    self.logger.error("📋 黑名单窗口列表:")
                    for blacklisted in blacklisted_windows:
                        self.logger.error(f"  🚫 {blacklisted}")
                    return False

                # 🆕 如果到达窗口列表末尾，重新开始循环
                if window_index >= len(windows):
                    window_index = 0
                    cycle_count += 1
                    self.logger.info(f"🔄 完成第 {cycle_count} 轮窗口循环，重新开始下一轮")
                    # 🆕 修复进度统计：显示"已处理/剩余总数"格式
                    remaining_contacts = total_contacts - total_processed
                    self.logger.info(f"📊 当前进度：已处理 {total_processed}/{remaining_contacts} 个联系人（剩余 {remaining_contacts} 个）")

                # 🆕 跳过黑名单窗口
                window = windows[window_index]
                hwnd = window.get('hwnd')
                title = window.get('title', 'Unknown')
                # 确保hwnd是有效的整数
                if hwnd is not None and isinstance(hwnd, int):
                    if hasattr(self, 'frequency_handler') and self.frequency_handler.is_window_blacklisted(hwnd, title):
                        self.logger.warning(f"🚫 跳过黑名单窗口: {title} (句柄:{hwnd})")
                        window_index += 1
                        continue
                else:
                    # 如果hwnd无效，也跳过此窗口
                    self.logger.warning(f"⚠️ 窗口句柄无效，跳过: {title} (句柄:{hwnd})")
                    window_index += 1
                    continue

                window = windows[window_index]
                self.current_window_index = window_index

                self.logger.info(f"\n{'='*60}")
                self.logger.info(f"🎯 开始处理第 {window_index + 1}/{len(windows)} 个微信窗口 (第 {cycle_count + 1} 轮)")
                self.logger.info(f"📋 窗口信息: {window.get('title', 'Unknown')} (句柄: {window.get('hwnd', 'Unknown')})")
                # 🆕 修复进度统计：显示"已处理/剩余总数"格式
                remaining_contacts = total_contacts - total_processed
                self.logger.info(f"📊 全局进度：已处理 {total_processed}/{remaining_contacts} 个联系人（剩余 {remaining_contacts} 个）")
                self.logger.info(f"{'='*60}")

                # 执行单个窗口的完整流程
                result = self.execute_single_window_flow(window, contacts)

                # 🆕 强化频率错误处理：立即切换到下一个窗口
                if result == "RESTART_REQUIRED":
                    self.logger.warning(f"🔄 第 {window_index + 1} 个微信窗口检测到频率错误")
                    self.logger.info("🚪 频率错误处理完成，当前微信窗口已关闭")
                    self.logger.info("🔄 根据频率错误处理机制，立即切换到下一个微信窗口")

                    # 🆕 确保当前窗口状态已正确标记（在execute_single_window_flow中已设置）
                    # self.window_statuses[self.current_window_index] = WindowStatus.ERROR  # 已在上面设置

                    # 🆕 立即切换到下一个窗口
                    window_index += 1
                    self.logger.info("⏳ 频率错误后窗口切换延迟（5秒）...")
                    time.sleep(5)
                    continue

                # 🆕 处理联系人数量限制：切换到下一个窗口
                elif result == "WINDOW_SWITCH_REQUIRED":
                    self.logger.info(f"🔄 第 {window_index + 1} 个微信窗口达到联系人处理数量限制")
                    self.logger.info("📋 当前窗口联系人处理完成，准备切换到下一个微信窗口")

                    # 🆕 更新全局联系人处理统计
                    # 从配置中获取每个窗口处理的联系人数量
                    try:
                        import json
                        with open("config.json", 'r', encoding='utf-8') as f:
                            config = json.load(f)
                        contacts_per_window = config.get("multi_window", {}).get("contacts_per_window", 10)
                        total_processed += contacts_per_window
                        self.global_contact_index += contacts_per_window  # 🆕 更新全局索引
                        # 🆕 修复进度统计：显示"已处理/剩余总数"格式
                        remaining_contacts = total_contacts - total_processed
                        self.logger.info(f"� 更新全局进度：已处理 {total_processed}/{remaining_contacts} 个联系人（剩余 {remaining_contacts} 个）")
                    except Exception as e:
                        self.logger.warning(f"⚠️ 无法读取配置文件，使用默认值: {e}")
                        total_processed += 2  # 默认每个窗口处理2个联系人
                        self.global_contact_index += 2  # 🆕 更新全局索引

                    # 🆕 标记当前窗口为部分完成状态
                    self.window_statuses[self.current_window_index] = WindowStatus.COMPLETED

                    # 🆕 切换到下一个窗口
                    window_index += 1
                    self.logger.info("⏳ 正常窗口切换延迟（3秒）...")
                    time.sleep(3)

                    # 🆕 窗口切换后立即检查是否还有微信窗口
                    if hasattr(self, 'frequency_handler'):
                        current_window_count = len(self.get_wechat_windows())
                        self.logger.info(f"📊 窗口切换后检查: 当前微信窗口数量={current_window_count}")

                        if current_window_count == 0:
                            self.logger.error("🚨 窗口切换后检测到没有微信窗口")
                            self.logger.error("🚨 所有微信窗口都已关闭，程序立即终止")
                            self.logger.error(f"📊 最终统计：已处理 {total_processed}/{total_contacts} 个联系人")
                            return False

                    continue

                elif result:
                    self.logger.info(f"✅ 第 {window_index + 1} 个微信窗口处理成功")
                    # 🆕 正常完成，切换到下一个窗口
                    window_index += 1
                    self.logger.info("⏳ 正常窗口切换延迟（3秒）...")
                    time.sleep(3)
                else:
                    self.logger.error(f"❌ 第 {window_index + 1} 个微信窗口处理失败")
                    # 🆕 失败也要切换到下一个窗口
                    window_index += 1
                    self.logger.info("⏳ 失败后窗口切换延迟（3秒）...")
                    time.sleep(3)

            # 🆕 最终统计结果
            self.logger.info(f"\n{'='*60}")
            self.logger.info("📊 多窗口循环处理完成统计:")
            self.logger.info(f"  🔄 总循环轮数: {cycle_count + 1}")
            self.logger.info(f"  📞 总联系人数: {total_contacts}")
            self.logger.info(f"  ✅ 已处理联系人: {total_processed}")
            # 🆕 修复最终统计：显示正确的进度格式
            remaining_contacts = max(0, total_contacts - total_processed)
            processed_count = min(total_processed, total_contacts)
            self.logger.info(f"  📈 处理进度: {processed_count}/{total_contacts} 个联系人 ({processed_count/total_contacts*100:.1f}%)")
            self.logger.info(f"  📋 剩余联系人: {remaining_contacts} 个")
            self.logger.info(f"  🖥️ 使用窗口数: {len(windows)}")
            self.logger.info(f"{'='*60}")

            # 🆕 检查是否所有联系人都已处理
            all_processed = total_processed >= total_contacts
            if all_processed:
                self.logger.info("🎉 所有联系人处理完成！")
            else:
                self.logger.warning(f"⚠️ 仍有 {total_contacts - total_processed} 个联系人未处理")

            return all_processed

        except Exception as e:
            self.logger.error(f"❌ 多窗口处理异常: {e}")
            self.logger.error(traceback.format_exc())
            return False

    def print_execution_summary(self):
        """打印执行总结"""
        try:
            self.execution_stats["end_time"] = datetime.now()

            if self.execution_stats["start_time"]:
                duration = self.execution_stats["end_time"] - self.execution_stats["start_time"]
                duration_str = str(duration).split('.')[0]  # 去掉微秒
            else:
                duration_str = "未知"

            print(f"\n{'='*80}")
            print("🎉 微信自动化添加好友执行完成!")
            print(f"{'='*80}")
            print(f"📅 开始时间: {self.execution_stats['start_time']}")
            print(f"📅 结束时间: {self.execution_stats['end_time']}")
            print(f"⏱️ 执行时长: {duration_str}")
            print(f"📊 处理统计:")
            print(f"  🖥️ 总微信窗口数: {self.execution_stats['total_windows']}")
            print(f"  ✅ 成功处理窗口: {self.execution_stats['completed_windows']}")
            print(f"  📞 总联系人数: {self.execution_stats['total_contacts']}")
            print(f"  ✅ 成功添加好友: {self.execution_stats['successful_adds']}")
            print(f"  ❌ 添加失败: {self.execution_stats['failed_adds']}")
            print(f"  ⏭️ 跳过联系人: {self.execution_stats['skipped_contacts']}")

            if self.execution_stats['total_windows'] > 0:
                window_success_rate = self.execution_stats['completed_windows'] / self.execution_stats['total_windows'] * 100
                print(f"  📈 窗口成功率: {window_success_rate:.1f}%")

            if self.execution_stats['total_contacts'] > 0:
                contact_success_rate = self.execution_stats['successful_adds'] / self.execution_stats['total_contacts'] * 100
                print(f"  📈 联系人成功率: {contact_success_rate:.1f}%")

            print(f"📁 详细日志文件: logs/current/main_controller_*.log")
            print(f"📊 Excel结果文件: {self.excel_file}")
            print(f"{'='*80}")

        except Exception as e:
            self.logger.error(f"❌ 打印执行总结失败: {e}")

    def run(self) -> bool:
        """运行主控制流程"""
        try:
            self.logger.info("🚀 微信自动化添加好友主控制程序启动")
            self.logger.info(f"📅 启动时间: {self.beijing_time}")
            self.execution_stats["start_time"] = datetime.now()

            # 1. 获取微信窗口
            windows = self.get_wechat_windows()
            if not windows:
                self.logger.error("❌ 未找到微信窗口，程序退出")
                return False

            # 🆕 1.5. 第一步就全部移动所有微信窗口到指定坐标位置
            self.move_all_windows_to_target_position(windows)

            # 2. 加载联系人数据
            contacts = self.load_contacts_data()
            if not contacts:
                self.logger.error("❌ 未找到待处理联系人，程序退出")
                return False

            # 3. 执行多窗口循环处理
            success = self.execute_multi_window_flow(windows, contacts)

            # 4. 打印执行总结
            self.print_execution_summary()

            return success

        except KeyboardInterrupt:
            self.logger.info("⏹️ 用户中断程序执行")
            self.print_execution_summary()
            return False

        except Exception as e:
            self.logger.error(f"❌ 主控制流程执行异常: {e}")
            self.logger.error(traceback.format_exc())
            self.print_execution_summary()
            return False


def main():
    """主函数"""
    try:
        print("🚀 微信自动化添加好友主控制程序")
        print("=" * 50)
        print("功能特性:")
        print("✅ 严格6步骤顺序执行")
        print("✅ 多微信窗口循环处理")
        print("✅ 强制窗口置顶管理")
        print("✅ 智能错误处理和重试")
        print("✅ 实时进度跟踪和日志")
        print("✅ Excel数据管理和统计")
        print("=" * 50)

        # 检查Excel文件
        excel_file= "添加好友名单.xlsx"
        if not Path(excel_file).exists():
            print(f"❌ Excel文件不存在: {excel_file}")
            print("请确保Excel文件存在并包含以下列：")
            print("  - 手机号码")
            print("  - 姓名")
            print("  - 准考证")
            print("  - 验证信息")
            return False

        # 创建并运行主控制器
        controller = WeChatMainController(excel_file)
        success = controller.run()

        if success:
            print("\n🎉 程序执行完成!")
            return True
        else:
            print("\n❌ 程序执行失败!")
            return False

    except Exception as e:
        print(f"\n❌ 程序执行异常: {e}")
        traceback.print_exc()
        return False


if __name__ == "__main__":
    main()
